# Redis任务队列系统使用指南

## 概述

Redis任务队列系统基于Redis Streams实现，提供轻量级的任务分发和状态管理功能。支持任务优先级、延迟执行、重试机制和多消费者并行处理。

## 核心组件

### 1. TaskStatus - 任务状态管理
- 定义任务状态枚举（pending、running、completed、failed等）
- 管理任务元数据（创建时间、执行时间、重试次数等）
- 支持任务状态转换和序列化

### 2. TaskQueue - 任务队列
- 基于Redis Streams实现任务入队/出队
- 支持优先级队列（urgent、high、normal、low）
- 提供任务确认(ack)和拒绝(nack)机制
- 支持延迟任务和计划任务处理

### 3. ConsumerGroup - 消费者组管理
- 管理Redis Streams消费者组
- 支持多消费者并行处理
- 提供消息认领和超时处理

### 4. QueueManager - 队列管理器
- 统一管理队列和消费者组
- 注册和调度任务处理器
- 管理后台任务（计划任务处理、清理等）

## 快速开始

### 1. 启动Redis服务
```bash
# 使用Docker Compose启动
docker-compose up -d redis

# 或者直接启动Redis
redis-server
```

### 2. 启动队列工作进程
```bash
# 启动默认工作进程
python -m data_trans queue

# 指定消费者组和并发数
python -m data_trans queue --group workers --max-concurrent 20
```

### 3. 使用CLI工具管理任务

#### 入队任务
```bash
# 爬虫任务
python -m data_trans.queue.cli enqueue \
  --task-type crawl \
  --payload '{"url": "http://example.com"}' \
  --priority high

# 清洗任务
python -m data_trans.queue.cli enqueue \
  --task-type clean \
  --payload '{"raw_data": {"title": "Test"}, "clean_rules": [{"type": "remove_empty"}]}'

# 存储任务
python -m data_trans.queue.cli enqueue \
  --task-type storage \
  --payload '{"data": {"id": 1, "name": "test"}, "storage_type": "mongodb"}'
```

#### 查看任务状态
```bash
python -m data_trans.queue.cli status <task_id>
```

#### 取消任务
```bash
python -m data_trans.queue.cli cancel <task_id>
```

#### 查看队列信息
```bash
python -m data_trans.queue.cli info
```

#### 清理过期任务
```bash
python -m data_trans.queue.cli cleanup --retention-days 7
```

#### 生成演示任务
```bash
python -m data_trans.queue.cli demo --count 50
```

## 编程接口

### 基本使用

```python
import asyncio
from data_trans.config.settings import get_settings
from data_trans.queue import QueueManager, TaskPriority

async def main():
    # 创建队列管理器
    settings = get_settings()
    manager = QueueManager(settings.redis)

    # 连接Redis
    await manager.connect()

    try:
        # 入队任务
        task_id = await manager.enqueue_task(
            task_type="crawl",
            payload={"url": "http://example.com"},
            priority=TaskPriority.HIGH,
            max_retries=3,
        )

        print(f"任务已入队: {task_id}")

        # 查询任务状态
        task_status = await manager.get_task_status(task_id)
        if task_status:
            print(f"任务状态: {task_status.state}")

    finally:
        await manager.disconnect()

asyncio.run(main())
```

### 自定义任务处理器

```python
from data_trans.queue.task_status import TaskStatus

async def custom_task_handler(task_status: TaskStatus) -> dict:
    """自定义任务处理器"""
    payload = task_status.payload

    # 处理任务逻辑
    result = {
        "processed": True,
        "data": payload,
        "timestamp": datetime.utcnow().isoformat(),
    }

    return result

# 注册处理器
manager.register_task_handler("custom", custom_task_handler)
```

### 启动消费者

```python
async def start_consumer():
    manager = QueueManager(settings.redis)
    await manager.connect()

    # 注册处理器
    manager.register_task_handler("crawl", crawl_handler)
    manager.register_task_handler("clean", clean_handler)

    # 启动后台任务
    await manager.start_background_tasks()

    # 启动消费者
    await manager.start_consumer(
        group_name="workers",
        max_concurrent_tasks=10
    )

    # 保持运行
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        await manager.stop_background_tasks()
        await manager.disconnect()
```

## 任务类型

系统预定义了以下任务类型：

### 1. crawl - 爬虫任务
```json
{
  "url": "http://example.com",
  "crawler_type": "http"
}
```

### 2. clean - 数据清洗任务
```json
{
  "raw_data": {"title": "Test", "content": ""},
  "clean_rules": [
    {"type": "remove_empty"},
    {"type": "normalize_text"}
  ]
}
```

### 3. storage - 存储任务
```json
{
  "data": {"id": 1, "name": "test"},
  "storage_type": "mongodb",
  "collection": "items"
}
```

### 4. notification - 通知任务
```json
{
  "message": "Task completed",
  "recipients": ["<EMAIL>"],
  "type": "email"
}
```

### 5. batch - 批处理任务
```json
{
  "items": [1, 2, 3, 4, 5],
  "batch_size": 10
}
```

## 配置选项

### Redis配置
```python
# .env文件
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_QUEUE_DATABASE=1
REDIS_RESULT_DATABASE=2
REDIS_MAX_CONNECTIONS=100
```

### 队列配置
- `max_stream_length`: Stream最大长度（默认10000）
- `block_time`: 消费者阻塞时间（默认1000ms）
- `max_retries`: 最大重试次数（默认3）
- `retry_delay`: 重试延迟（默认1.0秒）

## 监控和维护

### 队列监控
```bash
# 查看队列长度
python -m data_trans.queue.cli info

# 查看特定任务状态
python -m data_trans.queue.cli status <task_id>
```

### 定期维护
```bash
# 处理计划任务
python -m data_trans.queue.cli process-scheduled

# 清理过期任务
python -m data_trans.queue.cli cleanup --retention-days 7
```

### 性能优化
1. 调整消费者并发数：`--max-concurrent`
2. 使用多个消费者组分担负载
3. 根据任务类型设置合适的优先级
4. 定期清理已完成的任务

## 故障处理

### 常见问题
1. **Redis连接失败**: 检查Redis服务状态和配置
2. **任务处理超时**: 调整任务处理器的超时设置
3. **消息积压**: 增加消费者数量或并发度
4. **重试次数过多**: 检查任务处理逻辑和错误处理

### 恢复机制
- 自动重试失败的任务
- 消费者组消息认领机制
- 计划任务自动处理
- 过期任务自动清理

## 最佳实践

1. **任务设计**: 保持任务幂等性，避免副作用
2. **错误处理**: 合理设置重试次数和延迟
3. **监控**: 定期检查队列长度和任务状态
4. **资源管理**: 控制并发数，避免资源耗尽
5. **数据清理**: 定期清理已完成的任务数据
