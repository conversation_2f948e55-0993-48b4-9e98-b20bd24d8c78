{".class": "MypyFile", "_fullname": "src.data_trans.queue", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConsumerGroup": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.consumer_group.ConsumerGroup", "kind": "Gdef"}, "QueueManager": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.queue_manager.QueueManager", "kind": "Gdef"}, "TaskPriority": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_status.TaskPriority", "kind": "Gdef"}, "TaskQueue": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_queue.TaskQueue", "kind": "Gdef"}, "TaskState": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_status.TaskState", "kind": "Gdef"}, "TaskStatus": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_status.TaskStatus", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.queue.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.queue.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.queue.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.queue.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.queue.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.queue.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.queue.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.queue.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "src\\data_trans\\queue\\__init__.py"}