{".class": "MypyFile", "_fullname": "redis.asyncio.connection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["repr_pieces", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.AbstractConnection", "name": "AbstractConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "redis.asyncio.connection.AbstractConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.AbstractConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "arg_types": ["redis.asyncio.connection.AbstractConnection", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "redis.exceptions.RedisError"}], "extra_attrs": null, "type_ref": "builtins.list"}, "redis.asyncio.connection._Sentinel"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", {".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["redis.asyncio.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "redis.asyncio.connection.Encoder"}, {".class": "UnionType", "items": ["redis.credentials.CredentialProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AbstractConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_read_destructive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.can_read_destructive", "name": "can_read_destructive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_read_destructive of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_health": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.check_health", "name": "check_health", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_health of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_connect_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.clear_connect_callbacks", "name": "clear_connect_callbacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear_connect_callbacks of AbstractConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.client_name", "name": "client_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "credential_provider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.credential_provider", "name": "credential_provider", "setter_type": null, "type": {".class": "UnionType", "items": ["redis.credentials.CredentialProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "db": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.db", "name": "db", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}}}, "disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "nowait"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.disconnect", "name": "disconnect", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "nowait"], "arg_types": ["redis.asyncio.connection.AbstractConnection", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "disconnect of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.encoder", "name": "encoder", "setter_type": null, "type": "redis.asyncio.connection.Encoder"}}, "health_check_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.health_check_interval", "name": "health_check_interval", "setter_type": null, "type": "builtins.float"}}, "is_connected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.is_connected", "name": "is_connected", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_connected of AbstractConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.AbstractConnection.is_connected", "name": "is_connected", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_connected of AbstractConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "next_health_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.next_health_check", "name": "next_health_check", "setter_type": null, "type": "builtins.float"}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.on_connect", "name": "on_connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_connect of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pack_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.pack_command", "name": "pack_command", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["redis.asyncio.connection.AbstractConnection", {".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pack_command of AbstractConnection", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pack_commands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commands"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.pack_commands", "name": "pack_commands", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commands"], "arg_types": ["redis.asyncio.connection.AbstractConnection", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pack_commands of AbstractConnection", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.password", "name": "password", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.pid", "name": "pid", "setter_type": null, "type": "builtins.int"}}, "read_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "disable_decoding", "timeout", "disconnect_on_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.read_response", "name": "read_response", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "disable_decoding", "timeout", "disconnect_on_error"], "arg_types": ["redis.asyncio.connection.AbstractConnection", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_response of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "redis_connect_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.redis_connect_func", "name": "redis_connect_func", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "register_connect_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.register_connect_callback", "name": "register_connect_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "arg_types": ["redis.asyncio.connection.AbstractConnection", {".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_connect_callback of AbstractConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "repr_pieces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.repr_pieces", "name": "repr_pieces", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "repr_pieces of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.AbstractConnection.repr_pieces", "name": "repr_pieces", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "repr_pieces of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.retry", "name": "retry", "setter_type": null, "type": "redis.asyncio.retry.Retry"}}, "retry_on_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.retry_on_error", "name": "retry_on_error", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "retry_on_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.retry_on_timeout", "name": "retry_on_timeout", "setter_type": null, "type": "builtins.bool"}}, "send_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.send_command", "name": "send_command", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["redis.asyncio.connection.AbstractConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_command of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_packed_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "command", "check_health"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.send_packed_command", "name": "send_packed_command", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "command", "check_health"], "arg_types": ["redis.asyncio.connection.AbstractConnection", {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_packed_command of AbstractConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.AbstractConnection.set_parser", "name": "set_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser_class"], "arg_types": ["redis.asyncio.connection.AbstractConnection", {".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_parser of AbstractConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket_connect_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.socket_connect_timeout", "name": "socket_connect_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "socket_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.socket_timeout", "name": "socket_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "username": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.AbstractConnection.username", "name": "username", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.AbstractConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsyncConnectCallbackProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.AsyncConnectCallbackProtocol", "name": "AsyncConnectCallbackProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "redis.asyncio.connection.AsyncConnectCallbackProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.AsyncConnectCallbackProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.AsyncConnectCallbackProtocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["redis.asyncio.connection.AsyncConnectCallbackProtocol", "redis.asyncio.connection.Connection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of AsyncConnectCallbackProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.AsyncConnectCallbackProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.AsyncConnectCallbackProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AuthenticationError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.AuthenticationError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["can_read_destructive", 1], ["on_connect", 1], ["on_disconnect", 1], ["read_response", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.BaseParser", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "redis.asyncio.connection.BaseParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.BaseParser", "builtins.object"], "names": {".class": "SymbolTable", "EXCEPTION_CLASSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.BaseParser.EXCEPTION_CLASSES", "name": "EXCEPTION_CLASSES", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ExceptionMappingT"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "socket_read_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.BaseParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "socket_read_size"], "arg_types": ["redis.asyncio.connection.BaseParser", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_read_destructive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.BaseParser.can_read_destructive", "name": "can_read_destructive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.BaseParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_read_destructive of BaseParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BaseParser.can_read_destructive", "name": "can_read_destructive", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.BaseParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_read_destructive of BaseParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.BaseParser.on_connect", "name": "on_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["redis.asyncio.connection.BaseParser", "redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_connect of BaseParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BaseParser.on_connect", "name": "on_connect", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["redis.asyncio.connection.BaseParser", "redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_connect of BaseParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.BaseParser.on_disconnect", "name": "on_disconnect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.BaseParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_disconnect of BaseParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BaseParser.on_disconnect", "name": "on_disconnect", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.BaseParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_disconnect of BaseParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parse_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.BaseParser.parse_error", "name": "parse_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_error of BaseParser", "ret_type": "redis.exceptions.ResponseError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BaseParser.parse_error", "name": "parse_error", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_error of BaseParser", "ret_type": "redis.exceptions.ResponseError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "read_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["self", "disable_decoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.BaseParser.read_response", "name": "read_response", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "disable_decoding"], "arg_types": ["redis.asyncio.connection.BaseParser", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_response of BaseParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, "redis.exceptions.ResponseError", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BaseParser.read_response", "name": "read_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "disable_decoding"], "arg_types": ["redis.asyncio.connection.BaseParser", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_response of BaseParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, "redis.exceptions.ResponseError", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.BaseParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.BaseParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlockingConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.BlockingConnectionPool", "name": "BlockingConnectionPool", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.BlockingConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.BlockingConnectionPool", "redis.asyncio.connection.ConnectionPool", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.BlockingConnectionPool.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 3, 5, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "max_connections", "timeout", "queue_class", "connection_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "max_connections", "timeout", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": ["redis.asyncio.connection.Connection"], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["redis.asyncio.connection.Connection", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "max_connections", "timeout", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": ["redis.asyncio.connection.Connection"], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["redis.asyncio.connection.Connection", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5, 4], "arg_names": ["self", "max_connections", "timeout", "connection_class", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["self", "max_connections", "timeout", "queue_class", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": ["redis.asyncio.connection.Connection"], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["redis.asyncio.connection.Connection", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BlockingConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.pool", "name": "pool", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}}, "queue_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.queue_class", "name": "queue_class", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.BlockingConnectionPool.timeout", "name": "timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.BlockingConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.BlockingConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.BlockingConnectionPool"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ConnectionT"], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectCallbackProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.ConnectCallbackProtocol", "name": "ConnectCallbackProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "redis.asyncio.connection.ConnectCallbackProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.ConnectCallbackProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectCallbackProtocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["redis.asyncio.connection.ConnectCallbackProtocol", "redis.asyncio.connection.Connection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ConnectCallbackProtocol", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectCallbackProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.ConnectCallbackProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectCallbackT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.asyncio.connection.ConnectCallbackT", "line": 84, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["redis.asyncio.connection.ConnectCallbackProtocol", "redis.asyncio.connection.AsyncConnectCallbackProtocol"], "uses_pep604_syntax": true}}}, "ConnectKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.ConnectKwargs", "name": "ConnectKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.ConnectKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.ConnectKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["username", "builtins.str"], ["password", "builtins.str"], ["connection_class", {".class": "TypeType", "item": "redis.asyncio.connection.AbstractConnection"}], ["host", "builtins.str"], ["port", "builtins.int"], ["db", "builtins.int"], ["path", "builtins.str"]], "readonly_keys": [], "required_keys": ["connection_class", "db", "host", "password", "path", "port", "username"]}}}, "Connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.asyncio.connection.AbstractConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.Connection", "name": "Connection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.Connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.Connection", "redis.asyncio.connection.AbstractConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.Connection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "arg_types": ["redis.asyncio.connection.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "redis.exceptions.RedisError"}], "extra_attrs": null, "type_ref": "builtins.list"}, "redis.asyncio.connection._Sentinel"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", {".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["redis.asyncio.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "redis.asyncio.connection.Encoder"}, {".class": "UnionType", "items": ["redis.credentials.CredentialProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Connection.host", "name": "host", "setter_type": null, "type": "builtins.str"}}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Connection.port", "name": "port", "setter_type": null, "type": "builtins.int"}}, "repr_pieces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.Connection.repr_pieces", "name": "repr_pieces", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.Connection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "repr_pieces of Connection", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket_keepalive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Connection.socket_keepalive", "name": "socket_keepalive", "setter_type": null, "type": "builtins.bool"}}, "socket_keepalive_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Connection.socket_keepalive_options", "name": "socket_keepalive_options", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "socket_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Connection.socket_type", "name": "socket_type", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.Connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.Connection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.ConnectionPool", "name": "ConnectionPool", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.ConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.ConnectionPool", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.ConnectionPool.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection_class", "max_connections", "connection_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "redis.asyncio.connection.ConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection_class", "max_connections", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.ConnectionPool.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection_class", "max_connections", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "max_connections", "connection_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "redis.asyncio.connection.ConnectionPool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "max_connections", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": ["redis.asyncio.connection.Connection"], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.ConnectionPool.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "max_connections", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": ["redis.asyncio.connection.Connection"], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection_class", "max_connections", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "max_connections", "connection_kwargs"], "arg_types": [{".class": "Instance", "args": ["redis.asyncio.connection.Connection"], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "connection_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.ConnectionPool.connection_class", "name": "connection_class", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}}}, "connection_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.ConnectionPool.connection_kwargs", "name": "connection_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "inuse_connections"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.disconnect", "name": "disconnect", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "inuse_connections"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "disconnect of ConnectionPool", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.ConnectionPool.encoder_class", "name": "encoder_class", "setter_type": null, "type": {".class": "TypeType", "item": "redis.asyncio.connection.Encoder"}}}, "from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "redis.asyncio.connection.ConnectionPool.from_url", "name": "from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "url", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "values": [], "variance": 0}}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_url of ConnectionPool", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.ConnectionPool.from_url", "name": "from_url", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "url", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "values": [], "variance": 0}}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_url of ConnectionPool", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "values": [], "variance": 0}]}}}}, "get_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "command_name", "keys", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.get_connection", "name": "get_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "command_name", "keys", "options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "builtins.object", "builtins.object", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_connection of ConnectionPool", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.get_encoder", "name": "get_encoder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_encoder of ConnectionPool", "ret_type": "redis.asyncio.connection.Encoder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.make_connection", "name": "make_connection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_connection of ConnectionPool", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.ConnectionPool.max_connections", "name": "max_connections", "setter_type": null, "type": "builtins.int"}}, "owns_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.owns_connection", "name": "owns_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "owns_connection of ConnectionPool", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.ConnectionPool.pid", "name": "pid", "setter_type": null, "type": "builtins.int"}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "release of ConnectionPool", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.ConnectionPool.set_retry", "name": "set_retry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "redis.asyncio.retry.Retry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_retry of ConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.ConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.connection.ConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ConnectionT"], "typeddict_type": null}}, "CredentialProvider": {".class": "SymbolTableNode", "cross_ref": "redis.credentials.CredentialProvider", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DefaultParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.DefaultParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "redis.asyncio.connection.PythonParser"}, {".class": "TypeType", "item": "redis.asyncio.connection.HiredisParser"}], "uses_pep604_syntax": false}}}, "EncodableT": {".class": "SymbolTableNode", "cross_ref": "redis.typing.EncodableT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EncodedT": {".class": "SymbolTableNode", "cross_ref": "redis.typing.EncodedT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Encoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.Encoder", "name": "Encoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.Encoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.Encoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "encoding_errors", "decode_responses"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.Encoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "encoding_errors", "decode_responses"], "arg_types": ["redis.asyncio.connection.Encoder", "builtins.str", "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Encoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.Encoder.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "force"], "arg_types": ["redis.asyncio.connection.Encoder", {".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decode of Encoder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decode_responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Encoder.decode_responses", "name": "decode_responses", "setter_type": null, "type": "builtins.bool"}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.Encoder.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["redis.asyncio.connection.Encoder", {".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "encode of Encoder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodedT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Encoder.encoding", "name": "encoding", "setter_type": null, "type": "builtins.str"}}, "encoding_errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.Encoder.encoding_errors", "name": "encoding_errors", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.Encoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.Encoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptionMappingT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.asyncio.connection.ExceptionMappingT", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "FALSE_STRINGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.FALSE_STRINGS", "name": "FALSE_STRINGS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HiredisParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.asyncio.connection.BaseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.HiredisParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.HiredisParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.HiredisParser", "redis.asyncio.connection.BaseParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "socket_read_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.HiredisParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "socket_read_size"], "arg_types": ["redis.asyncio.connection.HiredisParser", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HiredisParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_read_destructive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.HiredisParser.can_read_destructive", "name": "can_read_destructive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.HiredisParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_read_destructive of HiredisParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.HiredisParser.on_connect", "name": "on_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["redis.asyncio.connection.HiredisParser", "redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_connect of HiredisParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.HiredisParser.on_disconnect", "name": "on_disconnect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.HiredisParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_disconnect of HiredisParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_from_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.HiredisParser.read_from_socket", "name": "read_from_socket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.HiredisParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_from_socket of HiredisParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "disable_decoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.HiredisParser.read_response", "name": "read_response", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "disable_decoding"], "arg_types": ["redis.asyncio.connection.HiredisParser", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_response of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.HiredisParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.HiredisParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MODULE_EXPORTS_DATA_TYPES_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.MODULE_EXPORTS_DATA_TYPES_ERROR", "name": "MODULE_EXPORTS_DATA_TYPES_ERROR", "setter_type": null, "type": "builtins.str"}}, "MODULE_LOAD_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.MODULE_LOAD_ERROR", "name": "MODULE_LOAD_ERROR", "setter_type": null, "type": "builtins.str"}}, "MODULE_UNLOAD_NOT_POSSIBLE_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.MODULE_UNLOAD_NOT_POSSIBLE_ERROR", "name": "MODULE_UNLOAD_NOT_POSSIBLE_ERROR", "setter_type": null, "type": "builtins.str"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MappingProxyType": {".class": "SymbolTableNode", "cross_ref": "types.MappingProxyType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NO_AUTH_SET_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.NO_AUTH_SET_ERROR", "name": "NO_AUTH_SET_ERROR", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "redis.exceptions.AuthenticationError"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "NO_SUCH_MODULE_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.NO_SUCH_MODULE_ERROR", "name": "NO_SUCH_MODULE_ERROR", "setter_type": null, "type": "builtins.str"}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PythonParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.asyncio.connection.BaseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.PythonParser", "name": "PythonParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.PythonParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.PythonParser", "redis.asyncio.connection.BaseParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "socket_read_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.PythonParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "socket_read_size"], "arg_types": ["redis.asyncio.connection.PythonParser", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PythonParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_read_destructive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.PythonParser.can_read_destructive", "name": "can_read_destructive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.PythonParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_read_destructive of PythonParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.PythonParser.encoder", "name": "encoder", "setter_type": null, "type": {".class": "UnionType", "items": ["redis.asyncio.connection.Encoder", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.PythonParser.on_connect", "name": "on_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": ["redis.asyncio.connection.PythonParser", "redis.asyncio.connection.AbstractConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_connect of PythonParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.PythonParser.on_disconnect", "name": "on_disconnect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.PythonParser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_disconnect of PythonParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "disable_decoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.connection.PythonParser.read_response", "name": "read_response", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "disable_decoding"], "arg_types": ["redis.asyncio.connection.PythonParser", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_response of PythonParser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, "redis.exceptions.ResponseError", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.PythonParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.PythonParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RedisError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.RedisError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RedisSSLContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.RedisSSLContext", "name": "RedisSSLContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.RedisSSLContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.RedisSSLContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "keyfile", "certfile", "cert_reqs", "ca_certs", "ca_data", "check_hostname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.RedisSSLContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "keyfile", "certfile", "cert_reqs", "ca_certs", "ca_data", "check_hostname"], "arg_types": ["redis.asyncio.connection.RedisSSLContext", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection._SSLVerifyMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RedisSSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ca_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.RedisSSLContext.ca_certs", "name": "ca_certs", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ca_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.RedisSSLContext.ca_data", "name": "ca_data", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "cert_reqs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.RedisSSLContext.cert_reqs", "name": "cert_reqs", "setter_type": null, "type": "ssl.VerifyMode"}}, "certfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.RedisSSLContext.certfile", "name": "certfile", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "check_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.RedisSSLContext.check_hostname", "name": "check_hostname", "setter_type": null, "type": "builtins.bool"}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.RedisSSLContext.context", "name": "context", "setter_type": null, "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.RedisSSLContext.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.RedisSSLContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of RedisSSLContext", "ret_type": "ssl.SSLContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keyfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.RedisSSLContext.keyfile", "name": "keyfile", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.RedisSSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.RedisSSLContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.ResponseError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Retry": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.retry.Retry", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SENTINEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.SENTINEL", "name": "SENTINEL", "setter_type": null, "type": "builtins.object"}}, "SERVER_CLOSED_CONNECTION_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.SERVER_CLOSED_CONNECTION_ERROR", "name": "SERVER_CLOSED_CONNECTION_ERROR", "setter_type": null, "type": "builtins.str"}}, "SSLConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.asyncio.connection.Connection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.SSLConnection", "name": "SSLConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.SSLConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.SSLConnection", "redis.asyncio.connection.Connection", "redis.asyncio.connection.AbstractConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "ssl_keyfile", "ssl_certfile", "ssl_cert_reqs", "ssl_ca_certs", "ssl_ca_data", "ssl_check_hostname", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.SSLConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "ssl_keyfile", "ssl_certfile", "ssl_cert_reqs", "ssl_ca_certs", "ssl_ca_data", "ssl_check_hostname", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "arg_types": ["redis.asyncio.connection.SSLConnection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection._SSLVerifyMode"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "redis.exceptions.RedisError"}], "extra_attrs": null, "type_ref": "builtins.list"}, "redis.asyncio.connection._Sentinel"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", {".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["redis.asyncio.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "redis.asyncio.connection.Encoder"}, {".class": "UnionType", "items": ["redis.credentials.CredentialProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SSLConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ca_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.SSLConnection.ca_certs", "name": "ca_certs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ca_certs of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.SSLConnection.ca_certs", "name": "ca_certs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ca_certs of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ca_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.SSLConnection.ca_data", "name": "ca_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ca_data of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.SSLConnection.ca_data", "name": "ca_data", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ca_data of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cert_reqs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.SSLConnection.cert_reqs", "name": "cert_reqs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cert_reqs of SSLConnection", "ret_type": "ssl.VerifyMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.SSLConnection.cert_reqs", "name": "cert_reqs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cert_reqs of SSLConnection", "ret_type": "ssl.VerifyMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "certfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.SSLConnection.certfile", "name": "certfile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "certfile of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.SSLConnection.certfile", "name": "certfile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "certfile of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "check_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.SSLConnection.check_hostname", "name": "check_hostname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_hostname of SSLConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.SSLConnection.check_hostname", "name": "check_hostname", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_hostname of SSLConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "keyfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.connection.SSLConnection.keyfile", "name": "keyfile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "keyfile of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.asyncio.connection.SSLConnection.keyfile", "name": "keyfile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.SSLConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "keyfile of SSLConnection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ssl_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.SSLConnection.ssl_context", "name": "ssl_context", "setter_type": null, "type": "redis.asyncio.connection.RedisSSLContext"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.SSLConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.SSLConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SYM_CRLF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.SYM_CRLF", "name": "SYM_CRLF", "setter_type": null, "type": "builtins.bytes"}}, "SYM_DOLLAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.SYM_DOLLAR", "name": "SYM_DOLLAR", "setter_type": null, "type": "builtins.bytes"}}, "SYM_EMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.SYM_EMPTY", "name": "SYM_EMPTY", "setter_type": null, "type": "builtins.bytes"}}, "SYM_LF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.SYM_LF", "name": "SYM_LF", "setter_type": null, "type": "builtins.bytes"}}, "SYM_STAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "redis.asyncio.connection.SYM_STAR", "name": "SYM_STAR", "setter_type": null, "type": "builtins.bytes"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "URL_QUERY_ARGUMENT_PARSERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.URL_QUERY_ARGUMENT_PARSERS", "name": "URL_QUERY_ARGUMENT_PARSERS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "types.MappingProxyType"}}}, "UnixDomainSocketConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.asyncio.connection.Connection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection.UnixDomainSocketConnection", "name": "UnixDomainSocketConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.UnixDomainSocketConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection.UnixDomainSocketConnection", "redis.asyncio.connection.Connection", "redis.asyncio.connection.AbstractConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "path", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.UnixDomainSocketConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "path", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "arg_types": ["redis.asyncio.connection.UnixDomainSocketConnection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "redis.exceptions.RedisError"}], "extra_attrs": null, "type_ref": "builtins.list"}, "redis.asyncio.connection._Sentinel"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", {".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["redis.asyncio.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "redis.asyncio.connection.Encoder"}, {".class": "UnionType", "items": ["redis.credentials.CredentialProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UnixDomainSocketConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.connection.UnixDomainSocketConnection.path", "name": "path", "setter_type": null, "type": "builtins.str"}}, "repr_pieces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.connection.UnixDomainSocketConnection.repr_pieces", "name": "repr_pieces", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.connection.UnixDomainSocketConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "repr_pieces of UnixDomainSocketConnection", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection.UnixDomainSocketConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection.UnixDomainSocketConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ConnectionT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "name": "_ConnectionT", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}}, "_SSLVerifyMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.asyncio.connection._SSLVerifyMode", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "required"}], "uses_pep604_syntax": false}}}, "_Sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.connection._Sentinel", "name": "_Sentinel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "redis.asyncio.connection._Sentinel", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "redis.asyncio.connection", "mro": ["redis.asyncio.connection._Sentinel", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "redis.asyncio.connection._Sentinel.sentinel", "name": "sentinel", "setter_type": null, "type": "builtins.object"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._Sentinel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.connection._Sentinel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.connection.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "parse_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.parse_url", "name": "parse_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_url", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectKwargs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_hidden": true, "module_public": false}, "to_bool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.asyncio.connection.to_bool", "name": "to_bool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_bool", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\redis-stubs\\asyncio\\connection.pyi"}