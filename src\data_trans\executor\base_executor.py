"""
基础任务执行器抽象类

定义了所有任务执行器必须实现的接口，确保一致性和可扩展性。
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from datetime import datetime
from enum import Enum
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class ExecutionResult(BaseModel):
    """执行结果"""
    
    task_id: str = Field(description="任务ID")
    status: ExecutionStatus = Field(description="执行状态")
    start_time: datetime = Field(description="开始时间")
    end_time: Optional[datetime] = Field(default=None, description="结束时间")
    duration: Optional[float] = Field(default=None, description="执行时长(秒)")
    
    # 数据统计
    crawled_count: int = Field(default=0, description="爬取数据条数")
    cleaned_count: int = Field(default=0, description="清洗数据条数")
    stored_count: int = Field(default=0, description="存储数据条数")
    
    # 错误信息
    error_message: Optional[str] = Field(default=None, description="错误信息")
    error_details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    
    # 执行日志
    logs: List[str] = Field(default_factory=list, description="执行日志")
    
    # 监控指标
    metrics: Dict[str, Any] = Field(default_factory=dict, description="监控指标")
    
    def add_log(self, message: str) -> None:
        """添加日志"""
        timestamp = datetime.utcnow().isoformat()
        self.logs.append(f"[{timestamp}] {message}")
    
    def set_error(self, message: str, details: Optional[Dict[str, Any]] = None) -> None:
        """设置错误信息"""
        self.status = ExecutionStatus.FAILED
        self.error_message = message
        self.error_details = details or {}
        self.add_log(f"ERROR: {message}")
    
    def complete(self) -> None:
        """标记完成"""
        self.end_time = datetime.utcnow()
        if self.start_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
        
        if self.status == ExecutionStatus.RUNNING:
            self.status = ExecutionStatus.SUCCESS
        
        self.add_log(f"任务完成，状态: {self.status.value}")


class ExecutorConfig(BaseModel):
    """执行器配置基类"""
    
    # 基础配置
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟(秒)")
    timeout: float = Field(default=300.0, description="执行超时时间(秒)")
    
    # 并发控制
    max_concurrent_tasks: int = Field(default=10, description="最大并发任务数")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    enable_metrics: bool = Field(default=True, description="启用监控指标")
    
    # 钩子配置
    enable_hooks: bool = Field(default=True, description="启用执行钩子")


class BaseExecutor(ABC):
    """任务执行器基类
    
    所有任务执行器都必须继承此类并实现抽象方法。
    """
    
    def __init__(self, config: ExecutorConfig) -> None:
        """初始化执行器
        
        Args:
            config: 执行器配置
        """
        self.config = config
        self._running_tasks: Dict[str, ExecutionResult] = {}
        self._semaphore = asyncio.Semaphore(config.max_concurrent_tasks)
        
        # 设置日志级别
        logger.setLevel(getattr(logging, config.log_level.upper()))
    
    @abstractmethod
    async def execute_task(
        self, 
        task_id: str, 
        task_config: Dict[str, Any]
    ) -> ExecutionResult:
        """执行单个任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置
            
        Returns:
            执行结果
        """
        pass
    
    async def execute_batch(
        self, 
        tasks: List[Dict[str, Any]]
    ) -> List[ExecutionResult]:
        """批量执行任务
        
        Args:
            tasks: 任务列表，每个任务包含task_id和配置
            
        Returns:
            执行结果列表
        """
        async def _execute_single(task: Dict[str, Any]) -> ExecutionResult:
            task_id = task.get("task_id", f"task_{int(time.time())}")
            async with self._semaphore:
                return await self.execute_task(task_id, task)
        
        # 并发执行所有任务
        tasks_coroutines = [_execute_single(task) for task in tasks]
        results = await asyncio.gather(*tasks_coroutines, return_exceptions=True)
        
        # 处理异常结果
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                task_id = tasks[i].get("task_id", f"task_{i}")
                error_result = ExecutionResult(
                    task_id=task_id,
                    status=ExecutionStatus.FAILED,
                    start_time=datetime.utcnow(),
                    error_message=str(result)
                )
                error_result.complete()
                final_results.append(error_result)
            else:
                final_results.append(result)
        
        return final_results
    
    def get_task_status(self, task_id: str) -> Optional[ExecutionResult]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            执行结果，如果任务不存在则返回None
        """
        return self._running_tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, ExecutionResult]:
        """获取所有任务状态"""
        return self._running_tasks.copy()
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否取消成功
        """
        if task_id in self._running_tasks:
            result = self._running_tasks[task_id]
            if result.status == ExecutionStatus.RUNNING:
                result.status = ExecutionStatus.CANCELLED
                result.complete()
                return True
        return False
    
    @asynccontextmanager
    async def execution_context(
        self, 
        task_id: str
    ) -> AsyncGenerator[ExecutionResult, None]:
        """执行上下文管理器
        
        确保资源正确释放和状态更新。
        
        Args:
            task_id: 任务ID
            
        Yields:
            执行结果对象
        """
        result = ExecutionResult(
            task_id=task_id,
            status=ExecutionStatus.RUNNING,
            start_time=datetime.utcnow()
        )
        
        self._running_tasks[task_id] = result
        
        try:
            yield result
        except asyncio.TimeoutError:
            result.status = ExecutionStatus.TIMEOUT
            result.set_error("任务执行超时")
        except Exception as e:
            result.set_error(f"任务执行异常: {e}")
        finally:
            result.complete()
            # 保留任务结果一段时间，便于查询
            await asyncio.sleep(0.1)  # 确保状态更新完成
    
    async def cleanup(self) -> None:
        """清理资源"""
        # 取消所有运行中的任务
        for task_id, result in self._running_tasks.items():
            if result.status == ExecutionStatus.RUNNING:
                self.cancel_task(task_id)
        
        logger.info("执行器资源清理完成")
