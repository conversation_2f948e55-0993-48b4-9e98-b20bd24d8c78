{"task_id": "example_crawl_task_001", "task_type": "crawl_clean_store", "crawler_type": "web", "crawler_config": {"timeout": 30.0, "max_retries": 3, "rate_limit_per_second": 2.0, "user_agent": "DataTrans Example Bot/1.0"}, "urls": ["https://httpbin.org/json", "https://httpbin.org/user-agent", "https://httpbin.org/headers"], "cleaner_type": "text", "cleaner_config": {"strict_mode": false, "skip_invalid": true, "preserve_original": true}, "cleaning_rules": [{"name": "normalize_whitespace", "field": "data", "rule_type": "regex", "pattern": "\\s+", "replacement": " ", "priority": 1}, {"name": "remove_empty_fields", "field": "*", "rule_type": "filter", "condition": "not_empty", "priority": 2}], "storage_type": "mongodb", "storage_config": {"database": "data_trans_example"}, "raw_collection": "example_raw_data", "cleaned_collection": "example_cleaned_data", "max_retries": 3, "retry_delay": 1.0, "timeout": 300.0}