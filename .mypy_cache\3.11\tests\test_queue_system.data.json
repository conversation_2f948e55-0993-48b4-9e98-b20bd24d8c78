{".class": "MypyFile", "_fullname": "tests.test_queue_system", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.AsyncMock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConsumerGroup": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.consumer_group.ConsumerGroup", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MagicMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.MagicMock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "QueueManager": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.queue_manager.QueueManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RedisConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.config.settings.RedisConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TASK_HANDLERS": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_handlers.TASK_HANDLERS", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskPriority": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_status.TaskPriority", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskQueue": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_queue.TaskQueue", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskState": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_status.TaskState", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TaskStatus": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.queue.task_status.TaskStatus", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TestQueueManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_queue_system.TestQueueManager", "name": "Test<PERSON>ueueManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_queue_system.TestQueueManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_queue_system", "mro": ["tests.test_queue_system.TestQueueManager", "builtins.object"], "names": {".class": "SymbolTable", "test_queue_manager_basic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestQueueManager.test_queue_manager_basic", "name": "test_queue_manager_basic", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestQueueManager.test_queue_manager_basic", "name": "test_queue_manager_basic", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_queue_system.TestQueueManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_queue_system.TestQueueManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestTaskQueue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_queue_system.TestTaskQueue", "name": "TestTaskQueue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_queue_system.TestTaskQueue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_queue_system", "mro": ["tests.test_queue_system.TestTaskQueue", "builtins.object"], "names": {".class": "SymbolTable", "test_ack_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_queue", "consumer_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestTaskQueue.test_ack_task", "name": "test_ack_task", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestTaskQueue.test_ack_task", "name": "test_ack_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_cancel_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestTaskQueue.test_cancel_task", "name": "test_cancel_task", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestTaskQueue.test_cancel_task", "name": "test_cancel_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_dequeue_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_queue", "consumer_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestTaskQueue.test_dequeue_task", "name": "test_dequeue_task", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestTaskQueue.test_dequeue_task", "name": "test_dequeue_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_enqueue_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestTaskQueue.test_enqueue_task", "name": "test_enqueue_task", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestTaskQueue.test_enqueue_task", "name": "test_enqueue_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_nack_task_with_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_queue", "consumer_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestTaskQueue.test_nack_task_with_retry", "name": "test_nack_task_with_retry", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestTaskQueue.test_nack_task_with_retry", "name": "test_nack_task_with_retry", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_queue_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestTaskQueue.test_queue_info", "name": "test_queue_info", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestTaskQueue.test_queue_info", "name": "test_queue_info", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_scheduled_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.TestTaskQueue.test_scheduled_task", "name": "test_scheduled_task", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.TestTaskQueue.test_scheduled_task", "name": "test_scheduled_task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_queue_system.TestTaskQueue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_queue_system.TestTaskQueue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestTaskStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_queue_system.TestTaskStatus", "name": "TestTaskStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_queue_system.TestTaskStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_queue_system", "mro": ["tests.test_queue_system.TestTaskStatus", "builtins.object"], "names": {".class": "SymbolTable", "test_task_status_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_queue_system.TestTaskStatus.test_task_status_creation", "name": "test_task_status_creation", "type": null}}, "test_task_status_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_queue_system.TestTaskStatus.test_task_status_retry", "name": "test_task_status_retry", "type": null}}, "test_task_status_serialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_queue_system.TestTaskStatus.test_task_status_serialization", "name": "test_task_status_serialization", "type": null}}, "test_task_status_state_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_queue_system.TestTaskStatus.test_task_status_state_update", "name": "test_task_status_state_update", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_queue_system.TestTaskStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_queue_system.TestTaskStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_queue_system.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_queue_system.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_queue_system.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_queue_system.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_queue_system.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_queue_system.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "consumer_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["redis_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.consumer_group", "name": "consumer_group", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.test_queue_system.consumer_group", "name": "consumer_group", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pytest": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_queue_system.pytest", "name": "pytest", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}}}, "redis": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "redis_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_decorated"], "fullname": "tests.test_queue_system.redis_client", "name": "redis_client", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.test_queue_system.redis_client", "name": "redis_client", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "task_queue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["redis_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_queue_system.task_queue", "name": "task_queue", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.test_queue_system.task_queue", "name": "task_queue", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_queue_system.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "tests\\test_queue_system.py"}