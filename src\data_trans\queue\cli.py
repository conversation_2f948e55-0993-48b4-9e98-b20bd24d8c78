"""
队列管理CLI工具

提供命令行接口来管理Redis任务队列系统。
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from typing import Optional

import click
import redis.asyncio as redis

from ..config.settings import get_settings
from .queue_manager import QueueManager
from .task_handlers import TASK_HANDLERS
from .task_status import TaskPriority, TaskState

logger = logging.getLogger(__name__)


@click.group()
@click.option("--debug", is_flag=True, help="启用调试模式")
def cli(debug: bool):
    """Redis任务队列管理工具"""
    if debug:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)


@cli.command()
@click.option("--task-type", required=True, help="任务类型")
@click.option("--payload", required=True, help="任务载荷(JSON格式)")
@click.option(
    "--priority",
    type=click.Choice(["low", "normal", "high", "urgent"]),
    default="normal",
    help="任务优先级",
)
@click.option("--max-retries", type=int, default=3, help="最大重试次数")
@click.option("--retry-delay", type=float, default=1.0, help="重试延迟(秒)")
def enqueue(
    task_type: str, payload: str, priority: str, max_retries: int, retry_delay: float
):
    """将任务加入队列"""

    async def _enqueue():
        try:
            # 解析载荷
            payload_dict = json.loads(payload)

            # 创建队列管理器
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            # 入队任务
            task_id = await manager.enqueue_task(
                task_type=task_type,
                payload=payload_dict,
                priority=TaskPriority(priority),
                max_retries=max_retries,
                retry_delay=retry_delay,
            )

            click.echo(f"任务已入队: {task_id}")

        except Exception as e:
            click.echo(f"入队失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.disconnect()

    asyncio.run(_enqueue())


@cli.command()
@click.argument("task_id")
def status(task_id: str):
    """查看任务状态"""

    async def _status():
        try:
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            task_status = await manager.get_task_status(task_id)

            if not task_status:
                click.echo(f"任务不存在: {task_id}", err=True)
                sys.exit(1)

            # 格式化输出
            click.echo(f"任务ID: {task_status.task_id}")
            click.echo(f"任务类型: {task_status.task_type}")
            click.echo(f"状态: {task_status.state.value}")
            click.echo(f"优先级: {task_status.priority.value}")
            click.echo(f"创建时间: {task_status.created_at}")
            click.echo(f"开始时间: {task_status.started_at}")
            click.echo(f"完成时间: {task_status.completed_at}")
            click.echo(f"重试次数: {task_status.retry_count}/{task_status.max_retries}")

            if task_status.worker_id:
                click.echo(f"执行者: {task_status.worker_id}")

            if task_status.error_message:
                click.echo(f"错误信息: {task_status.error_message}")

            if task_status.result:
                click.echo(
                    f"执行结果: {json.dumps(task_status.result, indent=2, ensure_ascii=False)}"
                )

            # 执行时间统计
            exec_time = task_status.get_execution_time()
            if exec_time:
                click.echo(f"执行时间: {exec_time:.2f}秒")

            wait_time = task_status.get_wait_time()
            if wait_time:
                click.echo(f"等待时间: {wait_time:.2f}秒")

        except Exception as e:
            click.echo(f"查询失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.disconnect()

    asyncio.run(_status())


@cli.command()
@click.argument("task_id")
def cancel(task_id: str):
    """取消任务"""

    async def _cancel():
        try:
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            success = await manager.cancel_task(task_id)

            if success:
                click.echo(f"任务已取消: {task_id}")
            else:
                click.echo(f"取消失败: {task_id}", err=True)
                sys.exit(1)

        except Exception as e:
            click.echo(f"取消失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.disconnect()

    asyncio.run(_cancel())


@cli.command()
def info():
    """显示队列信息"""

    async def _info():
        try:
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            queue_info = await manager.get_queue_info()

            click.echo("队列信息:")
            click.echo("-" * 50)

            total_length = 0
            for priority, info in queue_info.items():
                length = info["length"]
                total_length += length
                click.echo(f"{priority.upper()}: {length} 个任务")

            click.echo(f"总计: {total_length} 个任务")

        except Exception as e:
            click.echo(f"获取信息失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.disconnect()

    asyncio.run(_info())


@cli.command()
@click.option("--group-name", default="default", help="消费者组名称")
@click.option("--consumer-name", help="消费者名称")
@click.option("--max-concurrent", type=int, default=10, help="最大并发任务数")
def worker(group_name: str, consumer_name: Optional[str], max_concurrent: int):
    """启动任务消费者"""

    async def _worker():
        try:
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            # 注册任务处理器
            for task_type, handler in TASK_HANDLERS.items():
                manager.register_task_handler(task_type, handler)

            # 启动后台任务
            await manager.start_background_tasks()

            # 启动消费者
            await manager.start_consumer(
                group_name=group_name,
                consumer_name=consumer_name,
                max_concurrent_tasks=max_concurrent,
            )

            click.echo(f"消费者已启动: {group_name}")
            click.echo("按 Ctrl+C 停止...")

            # 保持运行
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                click.echo("\n正在停止消费者...")

        except Exception as e:
            click.echo(f"启动失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.stop_background_tasks()
            await manager.disconnect()

    asyncio.run(_worker())


@cli.command()
@click.option("--retention-days", type=int, default=7, help="保留天数")
def cleanup(retention_days: int):
    """清理已完成的任务"""

    async def _cleanup():
        try:
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            if manager.task_queue:
                cleaned_count = await manager.task_queue.cleanup_completed_tasks(
                    retention_days
                )
                click.echo(f"清理了 {cleaned_count} 个过期任务")

        except Exception as e:
            click.echo(f"清理失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.disconnect()

    asyncio.run(_cleanup())


@cli.command()
def process_scheduled():
    """处理计划任务"""

    async def _process_scheduled():
        try:
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            if manager.task_queue:
                processed_count = await manager.task_queue.process_scheduled_tasks()
                click.echo(f"处理了 {processed_count} 个计划任务")

        except Exception as e:
            click.echo(f"处理失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.disconnect()

    asyncio.run(_process_scheduled())


@cli.command()
@click.option("--count", type=int, default=10, help="生成任务数量")
def demo(count: int):
    """生成演示任务"""

    async def _demo():
        try:
            settings = get_settings()
            manager = QueueManager(settings.redis)

            await manager.connect()

            # 生成不同类型的演示任务
            task_types = ["crawl", "clean", "storage", "notification", "batch"]

            for i in range(count):
                task_type = task_types[i % len(task_types)]

                if task_type == "crawl":
                    payload = {"url": f"http://example.com/page{i}"}
                elif task_type == "clean":
                    payload = {
                        "raw_data": {"title": f"Title {i}", "content": f"Content {i}"}
                    }
                elif task_type == "storage":
                    payload = {"data": {"id": i, "value": f"value_{i}"}}
                elif task_type == "notification":
                    payload = {
                        "message": f"Message {i}",
                        "recipients": ["<EMAIL>"],
                    }
                else:  # batch
                    payload = {"items": list(range(i * 10, (i + 1) * 10))}

                task_id = await manager.enqueue_task(task_type, payload)
                click.echo(f"创建任务: {task_id} ({task_type})")

            click.echo(f"已创建 {count} 个演示任务")

        except Exception as e:
            click.echo(f"创建失败: {e}", err=True)
            sys.exit(1)
        finally:
            await manager.disconnect()

    asyncio.run(_demo())


if __name__ == "__main__":
    cli()
