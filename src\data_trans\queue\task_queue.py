"""
Redis任务队列实现

基于Redis Streams实现的任务队列，支持任务的生产、消费、确认机制。
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import redis.asyncio as redis

from .consumer_group import ConsumerGroup
from .task_status import TaskPriority, TaskState, TaskStatus

logger = logging.getLogger(__name__)


class TaskQueue:
    """基于Redis Streams的任务队列"""

    def __init__(
        self,
        redis_client: redis.Redis,
        queue_name: str = "task_queue",
        status_prefix: str = "task_status",
        max_stream_length: int = 10000,
    ):
        """初始化任务队列

        Args:
            redis_client: Redis客户端
            queue_name: 队列名称（Stream名称）
            status_prefix: 任务状态存储前缀
            max_stream_length: Stream最大长度
        """
        self.redis_client = redis_client
        self.queue_name = queue_name
        self.status_prefix = status_prefix
        self.max_stream_length = max_stream_length

        # 不同优先级的队列
        self.priority_queues = {
            TaskPriority.URGENT: f"{queue_name}:urgent",
            TaskPriority.HIGH: f"{queue_name}:high",
            TaskPriority.NORMAL: f"{queue_name}:normal",
            TaskPriority.LOW: f"{queue_name}:low",
        }

    def _get_status_key(self, task_id: str) -> str:
        """获取任务状态存储键"""
        return f"{self.status_prefix}:{task_id}"

    def _get_queue_name(self, priority: TaskPriority) -> str:
        """根据优先级获取队列名称"""
        return self.priority_queues.get(
            priority, self.priority_queues[TaskPriority.NORMAL]
        )

    async def enqueue(
        self,
        task_type: str,
        payload: Dict[str, Any],
        task_id: Optional[str] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        scheduled_at: Optional[datetime] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """将任务加入队列

        Args:
            task_type: 任务类型
            payload: 任务载荷数据
            task_id: 任务ID，默认生成UUID
            priority: 任务优先级
            scheduled_at: 计划执行时间
            max_retries: 最大重试次数
            retry_delay: 重试延迟
            metadata: 任务元数据

        Returns:
            任务ID
        """
        if task_id is None:
            task_id = str(uuid.uuid4())

        # 创建任务状态
        task_status = TaskStatus(
            task_id=task_id,
            task_type=task_type,
            priority=priority,
            payload=payload,
            metadata=metadata or {},
            scheduled_at=scheduled_at,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

        try:
            # 保存任务状态到Redis Hash
            status_key = self._get_status_key(task_id)
            await self.redis_client.hset(status_key, mapping=task_status.to_dict())

            # 如果是延迟任务，先不加入队列
            if scheduled_at and scheduled_at > datetime.utcnow():
                logger.info(f"任务 {task_id} 计划在 {scheduled_at} 执行")
                return task_id

            # 将任务ID加入对应优先级的Stream
            queue_name = self._get_queue_name(priority)
            message_id = await self.redis_client.xadd(
                queue_name,
                {
                    "task_id": task_id,
                    "task_type": task_type,
                    "priority": priority.value,
                    "created_at": task_status.created_at.isoformat(),
                },
                maxlen=self.max_stream_length,
                approximate=True,
            )

            logger.info(f"任务 {task_id} 已加入队列 {queue_name}，消息ID: {message_id}")
            return task_id

        except Exception as e:
            logger.error(f"任务入队失败: {e}")
            raise

    async def dequeue(
        self,
        consumer_group: ConsumerGroup,
        timeout: int = 1000,
    ) -> Optional[TaskStatus]:
        """从队列中取出任务

        Args:
            consumer_group: 消费者组
            timeout: 超时时间(毫秒)

        Returns:
            任务状态对象，如果没有任务则返回None
        """
        try:
            # 按优先级顺序读取消息
            for priority in [
                TaskPriority.URGENT,
                TaskPriority.HIGH,
                TaskPriority.NORMAL,
                TaskPriority.LOW,
            ]:
                queue_name = self._get_queue_name(priority)

                # 临时创建消费者组读取这个优先级队列
                temp_consumer = ConsumerGroup(
                    self.redis_client,
                    queue_name,
                    consumer_group.group_name,
                    consumer_group.consumer_name,
                    block_time=0,  # 非阻塞
                    count=1,
                )

                # 确保消费者组存在
                await temp_consumer.create_group()

                # 读取消息
                messages = await temp_consumer.read_messages()

                if messages:
                    message_id, fields = messages[0]
                    task_id = fields.get(b"task_id", b"").decode()

                    if task_id:
                        # 获取任务状态
                        task_status = await self.get_task_status(task_id)
                        if task_status:
                            # 更新任务状态为运行中
                            task_status.update_state(TaskState.RUNNING)
                            task_status.worker_id = consumer_group.consumer_name
                            task_status.consumer_group = consumer_group.group_name

                            # 保存更新的状态
                            await self._save_task_status(task_status)

                            # 保存消息ID到任务状态中，用于后续确认
                            task_status.metadata["message_id"] = message_id
                            task_status.metadata["queue_name"] = queue_name

                            logger.info(
                                f"任务 {task_id} 被消费者 {consumer_group.consumer_name} 取出"
                            )
                            return task_status

            return None

        except Exception as e:
            logger.error(f"任务出队失败: {e}")
            return None

    async def ack(
        self, task_status: TaskStatus, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """确认任务完成

        Args:
            task_status: 任务状态
            result: 任务执行结果

        Returns:
            是否确认成功
        """
        try:
            # 更新任务状态为完成
            task_status.update_state(TaskState.COMPLETED, result=result)

            # 保存任务状态
            await self._save_task_status(task_status)

            # 确认消息
            message_id = task_status.metadata.get("message_id")
            queue_name = task_status.metadata.get("queue_name")

            if message_id and queue_name:
                consumer_group = ConsumerGroup(
                    self.redis_client,
                    queue_name,
                    task_status.consumer_group or "default",
                    task_status.worker_id or "unknown",
                )

                await consumer_group.acknowledge_message(message_id)

            logger.info(f"任务 {task_status.task_id} 确认完成")
            return True

        except Exception as e:
            logger.error(f"任务确认失败: {e}")
            return False

    async def nack(
        self,
        task_status: TaskStatus,
        error_message: str,
        error_traceback: Optional[str] = None,
        retry: bool = True,
    ) -> bool:
        """拒绝任务（标记为失败）

        Args:
            task_status: 任务状态
            error_message: 错误信息
            error_traceback: 错误堆栈
            retry: 是否重试

        Returns:
            是否处理成功
        """
        try:
            # 设置错误信息
            task_status.error_message = error_message
            task_status.error_traceback = error_traceback

            # 检查是否可以重试
            if retry and task_status.can_retry():
                # 增加重试次数
                task_status.increment_retry()

                # 计算重试延迟
                delay = task_status.retry_delay * (
                    2 ** (task_status.retry_count - 1)
                )  # 指数退避
                scheduled_at = datetime.utcnow() + timedelta(seconds=delay)
                task_status.scheduled_at = scheduled_at

                # 保存状态
                await self._save_task_status(task_status)

                # 重新入队（延迟执行）
                await self.enqueue(
                    task_type=task_status.task_type,
                    payload=task_status.payload,
                    task_id=task_status.task_id,
                    priority=task_status.priority,
                    scheduled_at=scheduled_at,
                    max_retries=task_status.max_retries,
                    retry_delay=task_status.retry_delay,
                    metadata=task_status.metadata,
                )

                logger.info(f"任务 {task_status.task_id} 将在 {delay} 秒后重试")

            else:
                # 标记为失败
                task_status.update_state(TaskState.FAILED, error_message=error_message)
                await self._save_task_status(task_status)

                logger.error(f"任务 {task_status.task_id} 执行失败: {error_message}")

            # 确认消息（无论是否重试都要确认，避免消息重复投递）
            message_id = task_status.metadata.get("message_id")
            queue_name = task_status.metadata.get("queue_name")

            if message_id and queue_name:
                consumer_group = ConsumerGroup(
                    self.redis_client,
                    queue_name,
                    task_status.consumer_group or "default",
                    task_status.worker_id or "unknown",
                )

                await consumer_group.acknowledge_message(message_id)

            return True

        except Exception as e:
            logger.error(f"任务拒绝处理失败: {e}")
            return False

    async def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态对象
        """
        try:
            status_key = self._get_status_key(task_id)
            status_data = await self.redis_client.hgetall(status_key)

            if not status_data:
                return None

            # 转换字节数据为字符串
            status_dict = {
                k.decode() if isinstance(k, bytes) else k: (
                    v.decode() if isinstance(v, bytes) else v
                )
                for k, v in status_data.items()
            }

            # 处理特殊字段
            for field in [
                "created_at",
                "started_at",
                "completed_at",
                "updated_at",
                "scheduled_at",
            ]:
                if field in status_dict and status_dict[field]:
                    status_dict[field] = datetime.fromisoformat(status_dict[field])

            for field in ["retry_count", "max_retries"]:
                if field in status_dict:
                    status_dict[field] = int(status_dict[field])

            for field in ["retry_delay"]:
                if field in status_dict:
                    status_dict[field] = float(status_dict[field])

            for field in ["payload", "metadata", "result"]:
                if field in status_dict and status_dict[field]:
                    status_dict[field] = json.loads(status_dict[field])

            return TaskStatus.from_dict(status_dict)

        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return None

    async def _save_task_status(self, task_status: TaskStatus) -> bool:
        """保存任务状态

        Args:
            task_status: 任务状态对象

        Returns:
            是否保存成功
        """
        try:
            status_key = self._get_status_key(task_status.task_id)
            status_dict = task_status.to_dict()

            # 序列化复杂字段
            for field in ["payload", "metadata", "result"]:
                if field in status_dict and status_dict[field] is not None:
                    status_dict[field] = json.dumps(status_dict[field])

            await self.redis_client.hset(status_key, mapping=status_dict)
            return True

        except Exception as e:
            logger.error(f"保存任务状态失败: {e}")
            return False

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务

        Args:
            task_id: 任务ID

        Returns:
            是否取消成功
        """
        try:
            task_status = await self.get_task_status(task_id)
            if not task_status:
                logger.warning(f"任务 {task_id} 不存在")
                return False

            if task_status.is_finished():
                logger.warning(f"任务 {task_id} 已完成，无法取消")
                return False

            # 更新状态为取消
            task_status.update_state(TaskState.CANCELLED)
            await self._save_task_status(task_status)

            logger.info(f"任务 {task_id} 已取消")
            return True

        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False

    async def get_queue_length(self, priority: Optional[TaskPriority] = None) -> int:
        """获取队列长度

        Args:
            priority: 优先级，None表示所有队列

        Returns:
            队列长度
        """
        try:
            if priority:
                queue_name = self._get_queue_name(priority)
                return await self.redis_client.xlen(queue_name)
            else:
                total_length = 0
                for p in TaskPriority:
                    queue_name = self._get_queue_name(p)
                    total_length += await self.redis_client.xlen(queue_name)
                return total_length

        except Exception as e:
            logger.error(f"获取队列长度失败: {e}")
            return 0

    async def get_queue_info(self) -> Dict[str, Any]:
        """获取队列信息

        Returns:
            队列信息字典
        """
        try:
            info = {}

            for priority in TaskPriority:
                queue_name = self._get_queue_name(priority)
                length = await self.redis_client.xlen(queue_name)

                info[priority.value] = {
                    "queue_name": queue_name,
                    "length": length,
                }

            return info

        except Exception as e:
            logger.error(f"获取队列信息失败: {e}")
            return {}

    async def process_scheduled_tasks(self) -> int:
        """处理计划任务（将到期的延迟任务加入队列）

        Returns:
            处理的任务数量
        """
        try:
            # 查找所有计划任务
            pattern = f"{self.status_prefix}:*"
            keys = []

            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)

            processed_count = 0
            current_time = datetime.utcnow()

            for key in keys:
                status_data = await self.redis_client.hgetall(key)
                if not status_data:
                    continue

                # 检查是否是计划任务
                scheduled_at_str = status_data.get(b"scheduled_at") or status_data.get(
                    "scheduled_at"
                )
                state = status_data.get(b"state") or status_data.get("state")

                if not scheduled_at_str or not state:
                    continue

                if isinstance(scheduled_at_str, bytes):
                    scheduled_at_str = scheduled_at_str.decode()
                if isinstance(state, bytes):
                    state = state.decode()

                # 只处理待处理或重试中的计划任务
                if state not in [TaskState.PENDING.value, TaskState.RETRYING.value]:
                    continue

                try:
                    scheduled_at = datetime.fromisoformat(scheduled_at_str)
                    if scheduled_at <= current_time:
                        # 获取完整任务状态
                        task_id = key.decode().split(":")[-1]
                        task_status = await self.get_task_status(task_id)

                        if task_status:
                            # 清除计划时间
                            task_status.scheduled_at = None
                            await self._save_task_status(task_status)

                            # 加入队列
                            queue_name = self._get_queue_name(task_status.priority)
                            await self.redis_client.xadd(
                                queue_name,
                                {
                                    "task_id": task_status.task_id,
                                    "task_type": task_status.task_type,
                                    "priority": task_status.priority.value,
                                    "created_at": task_status.created_at.isoformat(),
                                },
                                maxlen=self.max_stream_length,
                                approximate=True,
                            )

                            processed_count += 1
                            logger.info(f"计划任务 {task_id} 已加入队列")

                except ValueError:
                    # 忽略无效的时间格式
                    continue

            if processed_count > 0:
                logger.info(f"处理了 {processed_count} 个计划任务")

            return processed_count

        except Exception as e:
            logger.error(f"处理计划任务失败: {e}")
            return 0

    async def cleanup_completed_tasks(self, retention_days: int = 7) -> int:
        """清理已完成的任务

        Args:
            retention_days: 保留天数

        Returns:
            清理的任务数量
        """
        try:
            pattern = f"{self.status_prefix}:*"
            keys = []

            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)

            cleaned_count = 0
            cutoff_time = datetime.utcnow() - timedelta(days=retention_days)

            for key in keys:
                status_data = await self.redis_client.hgetall(key)
                if not status_data:
                    continue

                state = status_data.get(b"state") or status_data.get("state")
                completed_at_str = status_data.get(b"completed_at") or status_data.get(
                    "completed_at"
                )

                if not state or not completed_at_str:
                    continue

                if isinstance(state, bytes):
                    state = state.decode()
                if isinstance(completed_at_str, bytes):
                    completed_at_str = completed_at_str.decode()

                # 只清理已完成的任务
                if state not in [
                    TaskState.COMPLETED.value,
                    TaskState.FAILED.value,
                    TaskState.CANCELLED.value,
                ]:
                    continue

                try:
                    completed_at = datetime.fromisoformat(completed_at_str)
                    if completed_at < cutoff_time:
                        await self.redis_client.delete(key)
                        cleaned_count += 1

                except ValueError:
                    # 忽略无效的时间格式
                    continue

            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期任务")

            return cleaned_count

        except Exception as e:
            logger.error(f"清理任务失败: {e}")
            return 0
