{"data_mtime": 1752386759, "dep_lines": [13, 14, 15, 16, 11, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.data_trans.config.settings", "src.data_trans.queue.consumer_group", "src.data_trans.queue.task_queue", "src.data_trans.queue.task_status", "redis.asyncio", "asyncio", "logging", "typing", "redis", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.locks", "asyncio.mixins", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "redis.asyncio.client", "redis.asyncio.connection", "redis.asyncio.retry", "redis.client", "redis.commands", "redis.commands.core", "redis.commands.redismodules", "redis.commands.sentinel", "redis.credentials", "redis.exceptions", "src.data_trans.config", "types"], "hash": "b07c4f39c934ca5de52aace18236dd964bc07c3c", "id": "src.data_trans.queue.queue_manager", "ignore_all": false, "interface_hash": "c9f22b9d163bee92a0c57c91adfe3c0e9ff7a58a", "mtime": 1752386758, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\queue\\queue_manager.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 9738, "suppressed": [], "version_id": "1.16.1"}