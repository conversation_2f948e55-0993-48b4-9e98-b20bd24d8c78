"""
任务状态管理

定义任务状态枚举和任务元数据模型。
"""

import json
import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class TaskState(str, Enum):
    """任务状态枚举"""

    PENDING = "pending"  # 待处理
    RUNNING = "running"  # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消
    RETRYING = "retrying"  # 重试中


class TaskPriority(str, Enum):
    """任务优先级枚举"""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class TaskStatus(BaseModel):
    """任务状态和元数据模型"""

    task_id: str = Field(..., description="任务唯一标识")
    task_type: str = Field(..., description="任务类型")
    state: TaskState = Field(default=TaskState.PENDING, description="任务状态")
    priority: TaskPriority = Field(
        default=TaskPriority.NORMAL, description="任务优先级"
    )

    # 时间戳
    created_at: datetime = Field(
        default_factory=datetime.utcnow, description="创建时间"
    )
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    updated_at: datetime = Field(
        default_factory=datetime.utcnow, description="更新时间"
    )

    # 重试相关
    retry_count: int = Field(default=0, description="重试次数")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟(秒)")

    # 执行信息
    worker_id: Optional[str] = Field(default=None, description="执行worker ID")
    consumer_group: Optional[str] = Field(default=None, description="消费者组")

    # 结果和错误
    result: Optional[Dict[str, Any]] = Field(default=None, description="执行结果")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    error_traceback: Optional[str] = Field(default=None, description="错误堆栈")

    # 任务数据
    payload: Dict[str, Any] = Field(default_factory=dict, description="任务载荷数据")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="任务元数据")

    # 延迟执行
    scheduled_at: Optional[datetime] = Field(default=None, description="计划执行时间")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.model_dump(mode="json")

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return self.model_dump_json()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TaskStatus":
        """从字典创建实例"""
        return cls.model_validate(data)

    @classmethod
    def from_json(cls, json_str: str) -> "TaskStatus":
        """从JSON字符串创建实例"""
        return cls.model_validate_json(json_str)

    def update_state(
        self,
        new_state: TaskState,
        error_message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None,
    ) -> None:
        """更新任务状态"""
        old_state = self.state
        self.state = new_state
        self.updated_at = datetime.utcnow()

        if new_state == TaskState.RUNNING and old_state == TaskState.PENDING:
            self.started_at = self.updated_at
        elif new_state in [TaskState.COMPLETED, TaskState.FAILED, TaskState.CANCELLED]:
            self.completed_at = self.updated_at

        if error_message:
            self.error_message = error_message

        if result:
            self.result = result

        logger.info(f"任务 {self.task_id} 状态从 {old_state} 更新为 {new_state}")

    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.state == TaskState.FAILED and self.retry_count < self.max_retries

    def increment_retry(self) -> None:
        """增加重试次数"""
        self.retry_count += 1
        self.state = TaskState.RETRYING
        self.updated_at = datetime.utcnow()

        logger.info(f"任务 {self.task_id} 开始第 {self.retry_count} 次重试")

    def is_finished(self) -> bool:
        """检查任务是否已完成（成功或失败）"""
        return self.state in [
            TaskState.COMPLETED,
            TaskState.FAILED,
            TaskState.CANCELLED,
        ]

    def get_execution_time(self) -> Optional[float]:
        """获取执行时间（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    def get_wait_time(self) -> Optional[float]:
        """获取等待时间（秒）"""
        if self.created_at and self.started_at:
            return (self.started_at - self.created_at).total_seconds()
        return None
