{".class": "MypyFile", "_fullname": "redis.commands.cluster", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ACLCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core.ACLCommands", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClusterDataAccessCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.ClusterDataAccessCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.core.DataAccessCommands"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.cluster.ClusterDataAccessCommands", "name": "ClusterDataAccessCommands", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.ClusterDataAccessCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterDataAccessCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.cluster", "mro": ["redis.commands.cluster.ClusterDataAccessCommands", "redis.commands.core.DataAccessCommands", "redis.commands.core.BasicKeyCommands", "redis.commands.core.HyperlogCommands", "redis.commands.core.HashCommands", "redis.commands.core.GeoCommands", "redis.commands.core.ListCommands", "redis.commands.core.ScanCommands", "redis.commands.core.SetCommands", "redis.commands.core.StreamCommands", "redis.commands.core.SortedSetCommands", "builtins.object"], "names": {".class": "SymbolTable", "stralgo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "algo", "value1", "value2", "specific_argument", "len", "idx", "minmatchlen", "<PERSON><PERSON><PERSON><PERSON>", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.ClusterDataAccessCommands.stralgo", "name": "stralgo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "algo", "value1", "value2", "specific_argument", "len", "idx", "minmatchlen", "<PERSON><PERSON><PERSON><PERSON>", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.ClusterDataAccessCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.ClusterDataAccessCommands"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stralgo of ClusterDataAccessCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.cluster.ClusterDataAccessCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.ClusterDataAccessCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.ClusterDataAccessCommands"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_StrType"], "typeddict_type": null}}, "ClusterManagementCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.commands.core.ManagementCommands"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.cluster.ClusterManagementCommands", "name": "ClusterManagementCommands", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterManagementCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.cluster", "mro": ["redis.commands.cluster.ClusterManagementCommands", "redis.commands.core.ManagementCommands", "builtins.object"], "names": {".class": "SymbolTable", "replicaof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.ClusterManagementCommands.replicaof", "name": "replicaof", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["redis.commands.cluster.ClusterManagementCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replicaof of ClusterManagementCommands", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "slaveof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.ClusterManagementCommands.slaveof", "name": "slaveof", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["redis.commands.cluster.ClusterManagementCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "slaveof of ClusterManagementCommands", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "swapdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.ClusterManagementCommands.swapdb", "name": "swapdb", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["redis.commands.cluster.ClusterManagementCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "swapdb of ClusterManagementCommands", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.cluster.ClusterManagementCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.cluster.ClusterManagementCommands", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClusterMultiKeyCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.cluster.ClusterMultiKeyCommands", "name": "ClusterMultiKeyCommands", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterMultiKeyCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.cluster", "mro": ["redis.commands.cluster.ClusterMultiKeyCommands", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterMultiKeyCommands.delete", "name": "delete", "type": null}}, "exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterMultiKeyCommands.exists", "name": "exists", "type": null}}, "mget_nonatomic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "keys", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterMultiKeyCommands.mget_nonatomic", "name": "mget_nonatomic", "type": null}}, "mset_nonatomic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterMultiKeyCommands.mset_nonatomic", "name": "mset_nonatomic", "type": null}}, "touch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterMultiKeyCommands.touch", "name": "touch", "type": null}}, "unlink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.ClusterMultiKeyCommands.unlink", "name": "unlink", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.cluster.ClusterMultiKeyCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.cluster.ClusterMultiKeyCommands", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataAccessCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core.DataAccessCommands", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ManagementCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core.ManagementCommands", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PubSubCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core.PubSubCommands", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RedisClusterCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.commands.cluster.ClusterMultiKeyCommands", "redis.commands.cluster.ClusterManagementCommands", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.core.ACLCommands"}, "redis.commands.core.PubSubCommands", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.ClusterDataAccessCommands"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.cluster.RedisClusterCommands", "name": "RedisClusterCommands", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.cluster", "mro": ["redis.commands.cluster.RedisClusterCommands", "redis.commands.cluster.ClusterMultiKeyCommands", "redis.commands.cluster.ClusterManagementCommands", "redis.commands.core.ManagementCommands", "redis.commands.core.ACLCommands", "redis.commands.core.PubSubCommands", "redis.commands.cluster.ClusterDataAccessCommands", "redis.commands.core.DataAccessCommands", "redis.commands.core.BasicKeyCommands", "redis.commands.core.HyperlogCommands", "redis.commands.core.HashCommands", "redis.commands.core.GeoCommands", "redis.commands.core.ListCommands", "redis.commands.core.ScanCommands", "redis.commands.core.SetCommands", "redis.commands.core.StreamCommands", "redis.commands.core.SortedSetCommands", "builtins.object"], "names": {".class": "SymbolTable", "cluster_addslots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "target_node", "slots"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_addslots", "name": "cluster_addslots", "type": null}}, "cluster_bumpepoch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_bumpepoch", "name": "cluster_bumpepoch", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_bumpepoch of RedisClusterCommands", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_count_failure_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_count_failure_report", "name": "cluster_count_failure_report", "type": null}}, "cluster_countkeysinslot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "slot_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_countkeysinslot", "name": "cluster_countkeysinslot", "type": null}}, "cluster_delslots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "slots"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_delslots", "name": "cluster_delslots", "type": null}}, "cluster_failover": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "target_node", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_failover", "name": "cluster_failover", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "target_node", "option"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_failover of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_flushslots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_flushslots", "name": "cluster_flushslots", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_flushslots of RedisClusterCommands", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_get_keys_in_slot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "slot", "num_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_get_keys_in_slot", "name": "cluster_get_keys_in_slot", "type": null}}, "cluster_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_info", "name": "cluster_info", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_info of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_keyslot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_keyslot", "name": "cluster_keyslot", "type": null}}, "cluster_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_links", "name": "cluster_links", "type": null}}, "cluster_meet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_meet", "name": "cluster_meet", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "port", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_meet of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_myshardid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_myshardid", "name": "cluster_myshardid", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_myshardid of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_nodes", "name": "cluster_nodes", "type": null}}, "cluster_replicas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node_id", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_replicas", "name": "cluster_replicas", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node_id", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_replicas of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_replicate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target_nodes", "node_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_replicate", "name": "cluster_replicate", "type": null}}, "cluster_reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "soft", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_reset", "name": "cluster_reset", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "soft", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_reset of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_save_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_save_config", "name": "cluster_save_config", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_save_config of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_set_config_epoch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "epoch", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_set_config_epoch", "name": "cluster_set_config_epoch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "epoch", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_set_config_epoch of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_setslot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "target_node", "node_id", "slot_id", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_setslot", "name": "cluster_setslot", "type": null}}, "cluster_setslot_stable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "slot_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_setslot_stable", "name": "cluster_setslot_stable", "type": null}}, "cluster_slots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.cluster_slots", "name": "cluster_slots", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cluster_slots of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_from_replicas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.cluster.RedisClusterCommands.read_from_replicas", "name": "read_from_replicas", "setter_type": null, "type": "builtins.bool"}}, "readonly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.readonly", "name": "readonly", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readonly of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readwrite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.cluster.RedisClusterCommands.readwrite", "name": "readwrite", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_nodes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readwrite of RedisClusterCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.cluster.RedisClusterCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.commands.cluster.RedisClusterCommands", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_StrType"], "typeddict_type": null}}, "_StrType": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core._StrType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.cluster.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.cluster.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.cluster.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.cluster.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.cluster.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.cluster.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\redis-stubs\\commands\\cluster.pyi"}