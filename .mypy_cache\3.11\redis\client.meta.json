{"data_mtime": 1752386592, "dep_lines": [3, 12, 13, 14, 15, 16, 17, 1, 2, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "redis.commands", "redis.connection", "redis.credentials", "redis.lock", "redis.retry", "redis.typing", "threading", "_typeshed", "datetime", "re", "types", "typing", "typing_extensions", "redis", "builtins", "_frozen_importlib", "abc", "redis.commands.core", "redis.commands.redismodules", "redis.commands.sentinel", "redis.exceptions"], "hash": "0ff1faef050e850f4ffcbbc995f4e7b33f7dc4ae", "id": "redis.client", "ignore_all": true, "interface_hash": "ea3324cad06c202680b2ba0d61ecd33a0a8740bd", "mtime": 1752386180, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\redis-stubs\\client.pyi", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 41520, "suppressed": [], "version_id": "1.16.1"}