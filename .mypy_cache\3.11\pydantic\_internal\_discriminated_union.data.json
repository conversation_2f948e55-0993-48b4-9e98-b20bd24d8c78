{".class": "MypyFile", "_fullname": "pydantic._internal._discriminated_union", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CoreMetadata": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_metadata.CoreMetadata", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CoreSchemaField": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.CoreSchemaField", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Discriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.types.Discriminator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MissingDefinitionForUnionRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._discriminated_union.MissingDefinitionForUnionRef", "name": "MissingDefinitionForUnionRef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._discriminated_union.MissingDefinitionForUnionRef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._discriminated_union", "mro": ["pydantic._internal._discriminated_union.MissingDefinitionForUnionRef", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union.MissingDefinitionForUnionRef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "arg_types": ["pydantic._internal._discriminated_union.MissingDefinitionForUnionRef", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MissingDefinitionForUnionRef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ref": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._discriminated_union.MissingDefinitionForUnionRef.ref", "name": "ref", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._discriminated_union.MissingDefinitionForUnionRef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._discriminated_union.MissingDefinitionForUnionRef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ApplyInferredDiscriminator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator", "name": "_ApplyInferredDiscriminator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._discriminated_union", "mro": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "discriminator", "definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "discriminator", "definitions"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _ApplyInferredDiscriminator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_to_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._apply_to_root", "name": "_apply_to_root", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_apply_to_root of _ApplyInferredDiscriminator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_choices_to_handle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._choices_to_handle", "name": "_choices_to_handle", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_discriminator_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._discriminator_alias", "name": "_discriminator_alias", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_handle_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "choice"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._handle_choice", "name": "_handle_choice", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "choice"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_handle_choice of _ApplyInferredDiscriminator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_infer_discriminator_values_for_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "choice", "source_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._infer_discriminator_values_for_choice", "name": "_infer_discriminator_values_for_choice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "choice", "source_name"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_infer_discriminator_values_for_choice of _ApplyInferredDiscriminator", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_infer_discriminator_values_for_dataclass_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "choice", "source_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._infer_discriminator_values_for_dataclass_choice", "name": "_infer_discriminator_values_for_dataclass_choice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "choice", "source_name"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassArgsSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_infer_discriminator_values_for_dataclass_choice of _ApplyInferredDiscriminator", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_infer_discriminator_values_for_field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._infer_discriminator_values_for_field", "name": "_infer_discriminator_values_for_field", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "source"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaField"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_infer_discriminator_values_for_field of _ApplyInferredDiscriminator", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_infer_discriminator_values_for_inner_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._infer_discriminator_values_for_inner_schema", "name": "_infer_discriminator_values_for_inner_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "source"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_infer_discriminator_values_for_inner_schema of _ApplyInferredDiscriminator", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_infer_discriminator_values_for_model_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "choice", "source_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._infer_discriminator_values_for_model_choice", "name": "_infer_discriminator_values_for_model_choice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "choice", "source_name"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelFieldsSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_infer_discriminator_values_for_model_choice of _ApplyInferredDiscriminator", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_infer_discriminator_values_for_typed_dict_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "choice", "source_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._infer_discriminator_values_for_typed_dict_choice", "name": "_infer_discriminator_values_for_typed_dict_choice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "choice", "source_name"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_infer_discriminator_values_for_typed_dict_choice of _ApplyInferredDiscriminator", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_discriminator_shared": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "choice"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._is_discriminator_shared", "name": "_is_discriminator_shared", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "choice"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TaggedUnionSchema"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_discriminator_shared of _ApplyInferredDiscriminator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._is_nullable", "name": "_is_nullable", "setter_type": null, "type": "builtins.bool"}}, "_set_unique_choice_for_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "choice", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._set_unique_choice_for_values", "name": "_set_unique_choice_for_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "choice", "values"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_unique_choice_for_values of _ApplyInferredDiscriminator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_be_nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._should_be_nullable", "name": "_should_be_nullable", "setter_type": null, "type": "builtins.bool"}}, "_tagged_union_choices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._tagged_union_choices", "name": "_tagged_union_choices", "setter_type": null, "type": {".class": "Instance", "args": ["<PERSON>.<PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_used": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator._used", "name": "_used", "setter_type": null, "type": "builtins.bool"}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._discriminated_union._ApplyInferredDiscriminator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "apply of _ApplyInferredDiscriminator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator.definitions", "name": "definitions", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "discriminator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator.discriminator", "name": "discriminator", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._discriminated_union._ApplyInferredDiscriminator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._discriminated_union.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._discriminated_union.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._discriminated_union.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._discriminated_union.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._discriminated_union.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._discriminated_union.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_core_utils": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils", "kind": "Gdef", "module_hidden": true, "module_public": false}, "apply_discriminator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["schema", "discriminator", "definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._discriminated_union.apply_discriminator", "name": "apply_discriminator", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["schema", "discriminator", "definitions"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", "pydantic.types.Discriminator"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "apply_discriminator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_hidden": true, "module_public": false}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "set_discriminator_in_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["schema", "discriminator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._discriminated_union.set_discriminator_in_metadata", "name": "set_discriminator_in_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["schema", "discriminator"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_discriminator_in_metadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py"}