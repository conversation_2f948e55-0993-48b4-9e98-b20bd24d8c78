"""
消费者组管理

管理Redis Streams的消费者组，支持多个worker并行处理任务。
"""

import asyncio
import logging
import uuid
from typing import Any, Dict, List, Optional, Tuple

import redis.asyncio as redis

from .task_status import TaskState, TaskStatus

logger = logging.getLogger(__name__)


class ConsumerGroup:
    """Redis Streams消费者组管理器"""

    def __init__(
        self,
        redis_client: redis.Redis,
        stream_name: str,
        group_name: str,
        consumer_name: Optional[str] = None,
        block_time: int = 1000,  # 阻塞时间(毫秒)
        count: int = 1,  # 每次读取消息数量
    ):
        """初始化消费者组

        Args:
            redis_client: Redis客户端
            stream_name: 流名称
            group_name: 消费者组名称
            consumer_name: 消费者名称，默认生成UUID
            block_time: 阻塞读取时间(毫秒)
            count: 每次读取的消息数量
        """
        self.redis_client = redis_client
        self.stream_name = stream_name
        self.group_name = group_name
        self.consumer_name = consumer_name or f"consumer-{uuid.uuid4().hex[:8]}"
        self.block_time = block_time
        self.count = count
        self._running = False

    async def create_group(self, start_id: str = "0") -> bool:
        """创建消费者组

        Args:
            start_id: 开始读取的消息ID，"0"表示从头开始，"$"表示从最新开始

        Returns:
            是否创建成功
        """
        try:
            await self.redis_client.xgroup_create(
                self.stream_name,
                self.group_name,
                start_id,
                mkstream=True,  # 如果流不存在则创建
            )
            logger.info(f"创建消费者组成功: {self.group_name}")
            return True

        except redis.ResponseError as e:
            if "BUSYGROUP" in str(e):
                logger.info(f"消费者组已存在: {self.group_name}")
                return True
            else:
                logger.error(f"创建消费者组失败: {e}")
                return False

        except Exception as e:
            logger.error(f"创建消费者组异常: {e}")
            return False

    async def read_messages(self) -> List[Tuple[str, Dict[str, Any]]]:
        """读取消息

        Returns:
            消息列表，每个元素为(message_id, fields)
        """
        try:
            # 首先尝试读取待处理的消息
            pending_messages = await self.redis_client.xreadgroup(
                self.group_name,
                self.consumer_name,
                {self.stream_name: ">"},
                count=self.count,
                block=self.block_time,
            )

            messages = []
            if pending_messages:
                for stream, stream_messages in pending_messages:
                    for message_id, fields in stream_messages:
                        messages.append((message_id.decode(), fields))

            return messages

        except Exception as e:
            logger.error(f"读取消息失败: {e}")
            return []

    async def acknowledge_message(self, message_id: str) -> bool:
        """确认消息处理完成

        Args:
            message_id: 消息ID

        Returns:
            是否确认成功
        """
        try:
            result = await self.redis_client.xack(
                self.stream_name, self.group_name, message_id
            )

            if result > 0:
                logger.debug(f"消息确认成功: {message_id}")
                return True
            else:
                logger.warning(f"消息确认失败: {message_id}")
                return False

        except Exception as e:
            logger.error(f"消息确认异常: {e}")
            return False

    async def get_pending_messages(self) -> List[Dict[str, Any]]:
        """获取待处理的消息

        Returns:
            待处理消息列表
        """
        try:
            pending_info = await self.redis_client.xpending(
                self.stream_name, self.group_name
            )

            if pending_info[0] == 0:  # 没有待处理消息
                return []

            # 获取详细的待处理消息信息
            pending_messages = await self.redis_client.xpending_range(
                self.stream_name, self.group_name, min="-", max="+", count=100
            )

            messages = []
            for msg_info in pending_messages:
                messages.append(
                    {
                        "message_id": msg_info[0].decode(),
                        "consumer": msg_info[1].decode(),
                        "idle_time": msg_info[2],  # 空闲时间(毫秒)
                        "delivery_count": msg_info[3],  # 投递次数
                    }
                )

            return messages

        except Exception as e:
            logger.error(f"获取待处理消息失败: {e}")
            return []

    async def claim_messages(
        self, idle_time: int = 60000
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """认领超时的消息

        Args:
            idle_time: 空闲时间阈值(毫秒)，超过此时间的消息将被认领

        Returns:
            认领的消息列表
        """
        try:
            # 获取待处理消息
            pending_messages = await self.get_pending_messages()

            # 找出超时的消息
            timeout_message_ids = [
                msg["message_id"]
                for msg in pending_messages
                if msg["idle_time"] > idle_time
            ]

            if not timeout_message_ids:
                return []

            # 认领超时消息
            claimed_messages = await self.redis_client.xclaim(
                self.stream_name,
                self.group_name,
                self.consumer_name,
                idle_time,
                *timeout_message_ids,
            )

            messages = []
            for message_id, fields in claimed_messages:
                messages.append((message_id.decode(), fields))

            logger.info(f"认领了 {len(messages)} 条超时消息")
            return messages

        except Exception as e:
            logger.error(f"认领消息失败: {e}")
            return []

    async def get_group_info(self) -> Optional[Dict[str, Any]]:
        """获取消费者组信息

        Returns:
            消费者组信息
        """
        try:
            groups = await self.redis_client.xinfo_groups(self.stream_name)

            for group in groups:
                if group[b"name"].decode() == self.group_name:
                    return {
                        "name": group[b"name"].decode(),
                        "consumers": group[b"consumers"],
                        "pending": group[b"pending"],
                        "last_delivered_id": group[b"last-delivered-id"].decode(),
                    }

            return None

        except Exception as e:
            logger.error(f"获取消费者组信息失败: {e}")
            return None

    async def get_consumers_info(self) -> List[Dict[str, Any]]:
        """获取消费者信息

        Returns:
            消费者信息列表
        """
        try:
            consumers = await self.redis_client.xinfo_consumers(
                self.stream_name, self.group_name
            )

            consumer_list = []
            for consumer in consumers:
                consumer_list.append(
                    {
                        "name": consumer[b"name"].decode(),
                        "pending": consumer[b"pending"],
                        "idle": consumer[b"idle"],
                    }
                )

            return consumer_list

        except Exception as e:
            logger.error(f"获取消费者信息失败: {e}")
            return []

    async def delete_consumer(self, consumer_name: Optional[str] = None) -> bool:
        """删除消费者

        Args:
            consumer_name: 消费者名称，默认为当前消费者

        Returns:
            是否删除成功
        """
        try:
            name = consumer_name or self.consumer_name
            result = await self.redis_client.xgroup_delconsumer(
                self.stream_name, self.group_name, name
            )

            logger.info(f"删除消费者成功: {name}")
            return result > 0

        except Exception as e:
            logger.error(f"删除消费者失败: {e}")
            return False

    async def delete_group(self) -> bool:
        """删除消费者组

        Returns:
            是否删除成功
        """
        try:
            result = await self.redis_client.xgroup_destroy(
                self.stream_name, self.group_name
            )

            logger.info(f"删除消费者组成功: {self.group_name}")
            return result > 0

        except Exception as e:
            logger.error(f"删除消费者组失败: {e}")
            return False
