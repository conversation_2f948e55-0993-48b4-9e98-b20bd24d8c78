"""
Redis任务队列系统测试

测试任务入队出队功能，验证状态转换正确性，测试多消费者并发处理，验证重试和错误处理机制。
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

import pytest
import redis.asyncio as redis

from src.data_trans.config.settings import RedisConfig
from src.data_trans.queue import (
    ConsumerGroup,
    QueueManager,
    TaskPriority,
    TaskQueue,
    TaskState,
    TaskStatus,
)
from src.data_trans.queue.task_handlers import TASK_HANDLERS


@pytest.fixture
async def redis_client():
    """Redis客户端fixture"""
    config = RedisConfig(
        host="localhost",
        port=6379,
        database=1,  # 使用测试数据库
    )

    client = redis.Redis(
        host=config.host,
        port=config.port,
        db=config.database,
        decode_responses=False,
    )

    # 清理测试数据
    await client.flushdb()

    yield client

    # 清理测试数据
    await client.flushdb()
    await client.close()


@pytest.fixture
async def task_queue(redis_client):
    """任务队列fixture"""
    return TaskQueue(redis_client, queue_name="test_queue")


@pytest.fixture
async def consumer_group(redis_client):
    """消费者组fixture"""
    group = ConsumerGroup(
        redis_client, "test_queue:normal", "test_group", "test_consumer"
    )
    await group.create_group()
    return group


class TestTaskStatus:
    """任务状态测试"""

    def test_task_status_creation(self):
        """测试任务状态创建"""
        task_status = TaskStatus(
            task_id="test_task_1",
            task_type="crawl",
            payload={"url": "http://example.com"},
        )

        assert task_status.task_id == "test_task_1"
        assert task_status.task_type == "crawl"
        assert task_status.state == TaskState.PENDING
        assert task_status.priority == TaskPriority.NORMAL
        assert task_status.retry_count == 0
        assert task_status.payload["url"] == "http://example.com"

    def test_task_status_state_update(self):
        """测试任务状态更新"""
        task_status = TaskStatus(
            task_id="test_task_2",
            task_type="clean",
        )

        # 更新为运行中
        task_status.update_state(TaskState.RUNNING)
        assert task_status.state == TaskState.RUNNING
        assert task_status.started_at is not None

        # 更新为完成
        result = {"processed": True}
        task_status.update_state(TaskState.COMPLETED, result=result)
        assert task_status.state == TaskState.COMPLETED
        assert task_status.completed_at is not None
        assert task_status.result == result

    def test_task_status_retry(self):
        """测试任务重试逻辑"""
        task_status = TaskStatus(
            task_id="test_task_3",
            task_type="storage",
            max_retries=3,
        )

        # 标记为失败
        task_status.update_state(TaskState.FAILED, error_message="Connection error")

        # 检查可以重试
        assert task_status.can_retry() is True

        # 增加重试次数
        task_status.increment_retry()
        assert task_status.retry_count == 1
        assert task_status.state == TaskState.RETRYING

        # 达到最大重试次数
        task_status.retry_count = 3
        task_status.update_state(TaskState.FAILED)
        assert task_status.can_retry() is False

    def test_task_status_serialization(self):
        """测试任务状态序列化"""
        task_status = TaskStatus(
            task_id="test_task_4",
            task_type="notification",
            payload={"message": "Hello"},
            metadata={"source": "test"},
        )

        # 转换为字典
        task_dict = task_status.to_dict()
        assert task_dict["task_id"] == "test_task_4"
        assert task_dict["payload"]["message"] == "Hello"

        # 从字典创建
        new_task_status = TaskStatus.from_dict(task_dict)
        assert new_task_status.task_id == task_status.task_id
        assert new_task_status.payload == task_status.payload

        # JSON序列化
        json_str = task_status.to_json()
        assert isinstance(json_str, str)

        # 从JSON创建
        from_json_task = TaskStatus.from_json(json_str)
        assert from_json_task.task_id == task_status.task_id


class TestTaskQueue:
    """任务队列测试"""

    @pytest.mark.asyncio
    async def test_enqueue_task(self, task_queue):
        """测试任务入队"""
        task_id = await task_queue.enqueue(
            task_type="crawl",
            payload={"url": "http://example.com"},
            priority=TaskPriority.HIGH,
        )

        assert task_id is not None

        # 检查任务状态
        task_status = await task_queue.get_task_status(task_id)
        assert task_status is not None
        assert task_status.task_type == "crawl"
        assert task_status.priority == TaskPriority.HIGH
        assert task_status.state == TaskState.PENDING

    @pytest.mark.asyncio
    async def test_dequeue_task(self, task_queue, consumer_group):
        """测试任务出队"""
        # 先入队一个任务
        task_id = await task_queue.enqueue(
            task_type="clean",
            payload={"data": "test_data"},
        )

        # 出队任务
        task_status = await task_queue.dequeue(consumer_group)

        assert task_status is not None
        assert task_status.task_id == task_id
        assert task_status.state == TaskState.RUNNING
        assert task_status.worker_id == consumer_group.consumer_name

    @pytest.mark.asyncio
    async def test_ack_task(self, task_queue, consumer_group):
        """测试任务确认"""
        # 入队并出队任务
        task_id = await task_queue.enqueue(
            task_type="storage",
            payload={"data": "test_data"},
        )

        task_status = await task_queue.dequeue(consumer_group)
        assert task_status is not None

        # 确认任务
        result = {"stored": True}
        success = await task_queue.ack(task_status, result)

        assert success is True

        # 检查任务状态
        updated_status = await task_queue.get_task_status(task_id)
        assert updated_status.state == TaskState.COMPLETED
        assert updated_status.result == result

    @pytest.mark.asyncio
    async def test_nack_task_with_retry(self, task_queue, consumer_group):
        """测试任务拒绝和重试"""
        # 入队并出队任务
        task_id = await task_queue.enqueue(
            task_type="notification",
            payload={"message": "test"},
            max_retries=2,
        )

        task_status = await task_queue.dequeue(consumer_group)
        assert task_status is not None

        # 拒绝任务（触发重试）
        success = await task_queue.nack(task_status, "Network error", retry=True)

        assert success is True

        # 检查任务状态
        updated_status = await task_queue.get_task_status(task_id)
        assert updated_status.retry_count == 1
        assert updated_status.error_message == "Network error"

    @pytest.mark.asyncio
    async def test_scheduled_task(self, task_queue):
        """测试计划任务"""
        # 创建延迟任务
        scheduled_time = datetime.utcnow() + timedelta(seconds=1)
        await task_queue.enqueue(  # unused task_id
            task_type="batch",
            payload={"items": [1, 2, 3]},
            scheduled_at=scheduled_time,
        )

        # 任务应该还没有在队列中
        queue_length = await task_queue.get_queue_length()
        assert queue_length == 0

        # 等待计划时间过去
        await asyncio.sleep(1.1)

        # 处理计划任务
        processed_count = await task_queue.process_scheduled_tasks()
        assert processed_count == 1

        # 现在任务应该在队列中
        queue_length = await task_queue.get_queue_length()
        assert queue_length == 1

    @pytest.mark.asyncio
    async def test_cancel_task(self, task_queue):
        """测试取消任务"""
        task_id = await task_queue.enqueue(
            task_type="crawl",
            payload={"url": "http://example.com"},
        )

        # 取消任务
        success = await task_queue.cancel_task(task_id)
        assert success is True

        # 检查任务状态
        task_status = await task_queue.get_task_status(task_id)
        assert task_status.state == TaskState.CANCELLED

    @pytest.mark.asyncio
    async def test_queue_info(self, task_queue):
        """测试队列信息"""
        # 添加不同优先级的任务
        await task_queue.enqueue("crawl", {"url": "1"}, priority=TaskPriority.HIGH)
        await task_queue.enqueue("crawl", {"url": "2"}, priority=TaskPriority.NORMAL)
        await task_queue.enqueue("crawl", {"url": "3"}, priority=TaskPriority.LOW)

        # 获取队列信息
        queue_info = await task_queue.get_queue_info()

        assert "high" in queue_info
        assert "normal" in queue_info
        assert "low" in queue_info
        assert queue_info["high"]["length"] == 1
        assert queue_info["normal"]["length"] == 1
        assert queue_info["low"]["length"] == 1


class TestQueueManager:
    """队列管理器测试"""

    @pytest.mark.asyncio
    async def test_queue_manager_basic(self):
        """测试队列管理器基本功能"""
        config = RedisConfig(database=1)  # 使用测试数据库
        manager = QueueManager(config)

        try:
            # 连接
            await manager.connect()

            # 注册任务处理器
            for task_type, handler in TASK_HANDLERS.items():
                manager.register_task_handler(task_type, handler)

            # 入队任务
            task_id = await manager.enqueue_task("crawl", {"url": "http://example.com"})

            assert task_id is not None

            # 获取任务状态
            task_status = await manager.get_task_status(task_id)
            assert task_status is not None
            assert task_status.task_type == "crawl"

        finally:
            await manager.disconnect()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
