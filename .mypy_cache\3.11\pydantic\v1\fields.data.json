{".class": "MypyFile", "_fullname": "pydantic.v1.fields", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSetIntStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.AbstractSetIntStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.BaseConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BoolUndefined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.v1.fields.BoolUndefined", "line": 94, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.bool", "pydantic.v1.fields.UndefinedType"], "uses_pep604_syntax": false}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CollectionCounter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CollectionsHashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CollectionsIterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConfigError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ConfigError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Counter": {".class": "SymbolTableNode", "cross_ref": "typing.Counter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DefaultDict": {".class": "SymbolTableNode", "cross_ref": "typing.DefaultDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DeferredType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.fields.DeferredType", "name": "DeferredType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.DeferredType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.fields", "mro": ["pydantic.v1.fields.DeferredType", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.DeferredType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.fields.DeferredType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ErrorList": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.error_wrappers.ErrorList", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ErrorWrapper": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.error_wrappers.ErrorWrapper", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["default", "default_factory", "alias", "title", "description", "exclude", "include", "const", "gt", "ge", "lt", "le", "multiple_of", "allow_inf_nan", "max_digits", "decimal_places", "min_items", "max_items", "unique_items", "min_length", "max_length", "allow_mutation", "regex", "discriminator", "repr", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.Field", "name": "Field", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["default", "default_factory", "alias", "title", "description", "exclude", "include", "const", "gt", "ge", "lt", "le", "multiple_of", "allow_inf_nan", "max_digits", "decimal_places", "min_items", "max_items", "unique_items", "min_length", "max_length", "allow_mutation", "regex", "discriminator", "repr", "extra"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.NoArgAnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "Field", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FieldInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.v1.utils.Representation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.fields.FieldInfo", "name": "FieldInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.FieldInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.fields", "mro": ["pydantic.v1.fields.FieldInfo", "pydantic.v1.utils.Representation", "builtins.object"], "names": {".class": "SymbolTable", "__field_constraints__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.FieldInfo.__field_constraints__", "name": "__field_constraints__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "default", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.FieldInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "default", "kwargs"], "arg_types": ["pydantic.v1.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FieldInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.FieldInfo.__repr_args__", "name": "__repr_args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.FieldInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr_args__ of FieldInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.ReprArgs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.v1.fields.FieldInfo.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.FieldInfo._validate", "name": "_validate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.FieldInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate of FieldInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.alias", "name": "alias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "alias_priority": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.alias_priority", "name": "alias_priority", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "allow_inf_nan": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.allow_inf_nan", "name": "allow_inf_nan", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "allow_mutation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.allow_mutation", "name": "allow_mutation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "const": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.const", "name": "const", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "decimal_places": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.decimal_places", "name": "decimal_places", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.default", "name": "default", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "default_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.default_factory", "name": "default_factory", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.description", "name": "description", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "discriminator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.discriminator", "name": "discriminator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "exclude": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.exclude", "name": "exclude", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "extra": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.extra", "name": "extra", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ge": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.ge", "name": "ge", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.FieldInfo.get_constraints", "name": "get_constraints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.FieldInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_constraints of FieldInfo", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.gt", "name": "gt", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "include": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.include", "name": "include", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "le": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.le", "name": "le", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "lt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.lt", "name": "lt", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "max_digits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.max_digits", "name": "max_digits", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "max_items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.max_items", "name": "max_items", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "max_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.max_length", "name": "max_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "min_items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.min_items", "name": "min_items", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "min_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.min_length", "name": "min_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "multiple_of": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.multiple_of", "name": "multiple_of", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "regex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.regex", "name": "regex", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "repr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.repr", "name": "repr", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "title": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.title", "name": "title", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "unique_items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.FieldInfo.unique_items", "name": "unique_items", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "update_from_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "from_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.FieldInfo.update_from_config", "name": "update_from_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "from_config"], "arg_types": ["pydantic.v1.fields.FieldInfo", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_from_config of FieldInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.FieldInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.fields.FieldInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ForwardRef": {".class": "SymbolTableNode", "cross_ref": "typing.ForwardRef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "InvalidDiscriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.InvalidDiscriminator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Json": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.Json", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JsonWrapper": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.JsonWrapper", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LocStr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.v1.fields.LocStr", "line": 93, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str"], "uses_pep604_syntax": false}}}, "MAPPING_LIKE_SHAPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.fields.MAPPING_LIKE_SHAPES", "name": "MAPPING_LIKE_SHAPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MappingIntStrAny": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.MappingIntStrAny", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MissingDiscriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.MissingDiscriminator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModelField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.v1.utils.Representation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.fields.ModelField", "name": "ModelField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.ModelField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.fields", "mro": ["pydantic.v1.fields.ModelField", "pydantic.v1.utils.Representation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "type_", "class_validators", "model_config", "default", "default_factory", "required", "final", "alias", "field_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "type_", "class_validators", "model_config", "default", "default_factory", "required", "final", "alias", "field_info"], "arg_types": ["pydantic.v1.fields.ModelField", "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "pydantic.v1.class_validators.Validator"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.NoArgAnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.BoolUndefined"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pydantic.v1.fields.FieldInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.__repr_args__", "name": "__repr_args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr_args__ of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.ReprArgs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.v1.fields.ModelField.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_apply_validators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls", "validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._apply_validators", "name": "_apply_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls", "validators"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorsList"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_apply_validators of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_sub_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "type_", "name", "for_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._create_sub_type", "name": "_create_sub_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "type_", "name", "for_keys"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_sub_type of ModelField", "ret_type": "pydantic.v1.fields.ModelField", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_field_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["field_name", "annotation", "value", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.v1.fields.ModelField._get_field_info", "name": "_get_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["field_name", "annotation", "value", "config"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_field_info of ModelField", "ret_type": {".class": "TupleType", "implicit": false, "items": ["pydantic.v1.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField._get_field_info", "name": "_get_field_info", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["field_name", "annotation", "value", "config"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_field_info of ModelField", "ret_type": {".class": "TupleType", "implicit": false, "items": ["pydantic.v1.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_mapping_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "original", "converted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._get_mapping_value", "name": "_get_mapping_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "original", "converted"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.ModelField._get_mapping_value", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_mapping_value of ModelField", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.ModelField._get_mapping_value", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.ModelField._get_mapping_value", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_set_default_and_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._set_default_and_type", "name": "_set_default_and_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_default_and_type of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_analysis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._type_analysis", "name": "_type_analysis", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_type_analysis of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._type_display", "name": "_type_display", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_type_display of ModelField", "ret_type": "pydantic.v1.utils.PyObjectStr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_discriminated_union": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._validate_discriminated_union", "name": "_validate_discriminated_union", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_discriminated_union of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_iterable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._validate_iterable", "name": "_validate_iterable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_iterable of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_mapping_like": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._validate_mapping_like", "name": "_validate_mapping_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_mapping_like of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_sequence_like": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._validate_sequence_like", "name": "_validate_sequence_like", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_sequence_like of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_singleton": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._validate_singleton", "name": "_validate_singleton", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_singleton of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField._validate_tuple", "name": "_validate_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "v", "values", "loc", "cls"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_tuple of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.alias", "name": "alias", "setter_type": null, "type": "builtins.str"}}, "allow_none": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.allow_none", "name": "allow_none", "setter_type": null, "type": "builtins.bool"}}, "alt_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.alt_alias", "name": "alt_alias", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "alt_alias of ModelF<PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.alt_alias", "name": "alt_alias", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "alt_alias of ModelF<PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "annotation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.ModelField.annotation", "name": "annotation", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "class_validators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.ModelField.class_validators", "name": "class_validators", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "pydantic.v1.class_validators.Validator"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.default", "name": "default", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "default_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.default_factory", "name": "default_factory", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.NoArgAnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "discriminator_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.discriminator_alias", "name": "discriminator_alias", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "discriminator_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.discriminator_key", "name": "discriminator_key", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "field_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.field_info", "name": "field_info", "setter_type": null, "type": "pydantic.v1.fields.FieldInfo"}}, "final": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.final", "name": "final", "setter_type": null, "type": "builtins.bool"}}, "get_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.get_default", "name": "get_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_default of ModelField", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.has_alias", "name": "has_alias", "setter_type": null, "type": "builtins.bool"}}, "infer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["cls", "name", "value", "annotation", "class_validators", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.infer", "name": "infer", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["cls", "name", "value", "annotation", "class_validators", "config"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.fields.ModelField"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "pydantic.v1.class_validators.Validator"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "infer of ModelField", "ret_type": "pydantic.v1.fields.ModelField", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.infer", "name": "infer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["cls", "name", "value", "annotation", "class_validators", "config"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.fields.ModelField"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "pydantic.v1.class_validators.Validator"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "infer of ModelField", "ret_type": "pydantic.v1.fields.ModelField", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_complex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.is_complex", "name": "is_complex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_complex of ModelField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_field": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.key_field", "name": "key_field", "setter_type": null, "type": {".class": "UnionType", "items": ["pydantic.v1.fields.ModelField", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.ModelField.model_config", "name": "model_config", "setter_type": null, "type": {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "outer_type_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.outer_type_", "name": "outer_type_", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "parse_json": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.parse_json", "name": "parse_json", "setter_type": null, "type": "builtins.bool"}}, "populate_validators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.populate_validators", "name": "populate_validators", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "populate_validators of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_validators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.post_validators", "name": "post_validators", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorsList"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pre_validators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.pre_validators", "name": "pre_validators", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorsList"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prepare of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_discriminated_union_sub_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.prepare_discriminated_union_sub_fields", "name": "prepare_discriminated_union_sub_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prepare_discriminated_union_sub_fields of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "required": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.required", "name": "required", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.BoolUndefined"}}}, "set_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.set_config", "name": "set_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_config of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.shape", "name": "shape", "setter_type": null, "type": "builtins.int"}}, "sub_fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.sub_fields", "name": "sub_fields", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["pydantic.v1.fields.ModelField"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sub_fields_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.sub_fields_mapping", "name": "sub_fields_mapping", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "pydantic.v1.fields.ModelField"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "type_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.type_", "name": "type_", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 5], "arg_names": ["self", "v", "values", "loc", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelField.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 5], "arg_names": ["self", "v", "values", "loc", "cls"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.LocStr"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate of ModelField", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.fields.ValidateReturn"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_always": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.validate_always", "name": "validate_always", "setter_type": null, "type": "builtins.bool"}}, "validators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.v1.fields.ModelField.validators", "name": "validators", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorsList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.ModelField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.fields.ModelField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelOrDc": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ModelOrDc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModelPrivateAttr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.v1.utils.Representation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.fields.ModelPrivateAttr", "name": "ModelPrivateAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.ModelPrivateAttr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.fields", "mro": ["pydantic.v1.fields.ModelPrivateAttr", "pydantic.v1.utils.Representation", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelPrivateAttr.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.v1.fields.ModelPrivateAttr", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of ModelPrivateAttr", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "default", "default_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelPrivateAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "default", "default_factory"], "arg_types": ["pydantic.v1.fields.ModelPrivateAttr", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.NoArgAnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModelPrivateAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.v1.fields.ModelPrivateAttr.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.ModelPrivateAttr.default", "name": "default", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "default_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.fields.ModelPrivateAttr.default_factory", "name": "default_factory", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.NoArgAnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.ModelPrivateAttr.get_default", "name": "get_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.ModelPrivateAttr"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_default of ModelPrivateAttr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.ModelPrivateAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.fields.ModelPrivateAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoArgAnyCallable": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.NoArgAnyCallable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoneIsNotAllowedError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NoneIsNotAllowedError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PrivateAttr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5], "arg_names": ["default", "default_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.PrivateAttr", "name": "PrivateAttr", "type": {".class": "CallableType", "arg_kinds": [1, 5], "arg_names": ["default", "default_factory"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.NoArgAnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "PrivateAttr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PyObjectStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.PyObjectStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReprArgs": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.ReprArgs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Representation": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.Representation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Required": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.fields.Required", "name": "Required", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "SHAPE_COUNTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_COUNTER", "name": "SHAPE_COUNTER", "setter_type": null, "type": "builtins.int"}}, "SHAPE_DEFAULTDICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_DEFAULTDICT", "name": "SHAPE_DEFAULTDICT", "setter_type": null, "type": "builtins.int"}}, "SHAPE_DEQUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_DEQUE", "name": "SHAPE_DEQUE", "setter_type": null, "type": "builtins.int"}}, "SHAPE_DICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_DICT", "name": "SHAPE_DICT", "setter_type": null, "type": "builtins.int"}}, "SHAPE_FROZENSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_FROZENSET", "name": "SHAPE_FROZENSET", "setter_type": null, "type": "builtins.int"}}, "SHAPE_GENERIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_GENERIC", "name": "SHAPE_GENERIC", "setter_type": null, "type": "builtins.int"}}, "SHAPE_ITERABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_ITERABLE", "name": "SHAPE_ITERABLE", "setter_type": null, "type": "builtins.int"}}, "SHAPE_LIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_LIST", "name": "SHAPE_LIST", "setter_type": null, "type": "builtins.int"}}, "SHAPE_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_MAPPING", "name": "SHAPE_MAPPING", "setter_type": null, "type": "builtins.int"}}, "SHAPE_NAME_LOOKUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_NAME_LOOKUP", "name": "SHAPE_NAME_LOOKUP", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SHAPE_SEQUENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_SEQUENCE", "name": "SHAPE_SEQUENCE", "setter_type": null, "type": "builtins.int"}}, "SHAPE_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_SET", "name": "SHAPE_SET", "setter_type": null, "type": "builtins.int"}}, "SHAPE_SINGLETON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_SINGLETON", "name": "SHAPE_SINGLETON", "setter_type": null, "type": "builtins.int"}}, "SHAPE_TUPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_TUPLE", "name": "SHAPE_TUPLE", "setter_type": null, "type": "builtins.int"}}, "SHAPE_TUPLE_ELLIPSIS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.SHAPE_TUPLE_ELLIPSIS", "name": "SHAPE_TUPLE_ELLIPSIS", "setter_type": null, "type": "builtins.int"}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_hidden": true, "module_public": false}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Undefined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.fields.Undefined", "name": "Undefined", "setter_type": null, "type": "pydantic.v1.fields.UndefinedType"}}, "UndefinedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.fields.UndefinedType", "name": "UndefinedType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.UndefinedType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.fields", "mro": ["pydantic.v1.fields.UndefinedType", "builtins.object"], "names": {".class": "SymbolTable", "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.UndefinedType.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.UndefinedType.__copy__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__copy__ of UndefinedType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.UndefinedType.__copy__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.UndefinedType.__copy__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.UndefinedType.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.UndefinedType.__deepcopy__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__deepcopy__ of UndefinedType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.UndefinedType.__deepcopy__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.T", "id": -1, "name": "T", "namespace": "pydantic.v1.fields.UndefinedType.__deepcopy__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.UndefinedType.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.fields.UndefinedType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__reduce__ of UndefinedType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.fields.UndefinedType.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.v1.fields.UndefinedType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of UndefinedType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.fields.UndefinedType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.fields.UndefinedType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValidateReturn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.v1.fields.ValidateReturn", "line": 92, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.error_wrappers.ErrorList"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.Validator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValidatorsList": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.ValidatorsList", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValueItems": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.ValueItems", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.fields.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.fields.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.fields.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.fields.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.fields.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.fields.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "constant_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.validators.constant_validator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "convert_generics": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.convert_generics", "kind": "Gdef", "module_hidden": true, "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dict_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.validators.dict_validator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "display_as_type": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.display_as_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "errors_": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors", "kind": "Gdef", "module_hidden": true, "module_public": false}, "find_validators": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.validators.find_validators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_args": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.get_args", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_discriminator_alias_and_values": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.get_discriminator_alias_and_values", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.get_origin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_unique_discriminator_alias": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.get_unique_discriminator_alias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_finalvar": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_finalvar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_finalvar_with_default_val": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["type_", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.fields.is_finalvar_with_default_val", "name": "is_finalvar_with_default_val", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["type_", "val"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_finalvar_with_default_val", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_literal_type": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_literal_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_new_type": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_new_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_none_type": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_none_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_typeddict": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_typeddict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_typeddict_special": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_typeddict_special", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_union": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "lenient_isinstance": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.lenient_isinstance", "kind": "Gdef", "module_hidden": true, "module_public": false}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.lenient_issubclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "make_generic_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.make_generic_validator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "new_type_supertype": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.new_type_supertype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "prep_validators": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.prep_validators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sequence_like": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.sequence_like", "kind": "Gdef", "module_hidden": true, "module_public": false}, "smart_deepcopy": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.smart_deepcopy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "validate_json": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.validators.validate_json", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\v1\\fields.py"}