{"data_mtime": 1752386759, "dep_lines": [2, 4, 1, 2, 3, 5, 6, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["unittest.case", "collections.abc", "sys", "unittest", "_typeshed", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "types"], "hash": "c52506b0c95b8ef1f134552ee5b070adb772e1b6", "id": "unittest.result", "ignore_all": true, "interface_hash": "2997525ecd79d62e2ec774291ae6b0302cb6ef20", "mtime": 1752386175, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\unittest\\result.pyi", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 2097, "suppressed": [], "version_id": "1.16.1"}