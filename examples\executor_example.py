#!/usr/bin/env python3
"""
TaskExecutor使用示例

演示如何使用TaskExecutor执行完整的数据采集到存储流程。
"""

import asyncio
import json
import logging
from pathlib import Path

from src.data_trans.executor import TaskExecutor, TaskExecutorConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def run_single_task_example():
    """运行单个任务示例"""
    logger.info("=== 单个任务执行示例 ===")
    
    # 创建执行器配置
    executor_config = TaskExecutorConfig(
        max_concurrent_tasks=5,
        timeout=300.0,
        enable_metrics=True,
        log_level="INFO"
    )
    
    # 创建执行器
    executor = TaskExecutor(executor_config)
    
    try:
        # 定义任务配置
        task_config = {
            "task_id": "demo_task_001",
            "crawler_type": "web",
            "urls": [
                "https://httpbin.org/json",
                "https://httpbin.org/user-agent"
            ],
            "cleaner_type": "text",
            "cleaning_rules": [
                {
                    "name": "normalize_text",
                    "field": "data",
                    "rule_type": "text",
                    "priority": 1
                }
            ],
            "storage_type": "mongodb",
            "raw_collection": "demo_raw",
            "cleaned_collection": "demo_cleaned"
        }
        
        # 执行任务
        result = await executor.execute_task("demo_task_001", task_config)
        
        # 输出结果
        logger.info(f"任务执行完成:")
        logger.info(f"  状态: {result.status.value}")
        logger.info(f"  爬取数据: {result.crawled_count} 条")
        logger.info(f"  清洗数据: {result.cleaned_count} 条")
        logger.info(f"  存储数据: {result.stored_count} 条")
        logger.info(f"  执行时长: {result.duration:.2f} 秒")
        
        if result.error_message:
            logger.error(f"  错误信息: {result.error_message}")
        
        # 输出执行日志
        logger.info("执行日志:")
        for log_entry in result.logs[-5:]:  # 显示最后5条日志
            logger.info(f"  {log_entry}")
            
    except Exception as e:
        logger.error(f"任务执行异常: {e}")
    
    finally:
        await executor.cleanup()


async def run_batch_tasks_example():
    """运行批量任务示例"""
    logger.info("=== 批量任务执行示例 ===")
    
    executor_config = TaskExecutorConfig(
        max_concurrent_tasks=3,
        timeout=180.0
    )
    
    executor = TaskExecutor(executor_config)
    
    try:
        # 定义多个任务
        tasks = [
            {
                "task_id": "batch_task_001",
                "crawler_type": "web",
                "urls": ["https://httpbin.org/json"],
                "storage_type": "mongodb",
                "raw_collection": "batch_raw_1"
            },
            {
                "task_id": "batch_task_002", 
                "crawler_type": "web",
                "urls": ["https://httpbin.org/headers"],
                "storage_type": "mongodb",
                "raw_collection": "batch_raw_2"
            },
            {
                "task_id": "batch_task_003",
                "crawler_type": "web", 
                "urls": ["https://httpbin.org/user-agent"],
                "storage_type": "mongodb",
                "raw_collection": "batch_raw_3"
            }
        ]
        
        # 批量执行任务
        results = await executor.execute_batch(tasks)
        
        # 输出结果统计
        logger.info(f"批量任务执行完成，共 {len(results)} 个任务:")
        
        success_count = 0
        total_crawled = 0
        total_stored = 0
        
        for result in results:
            logger.info(f"  任务 {result.task_id}: {result.status.value}")
            if result.status.value == "success":
                success_count += 1
            total_crawled += result.crawled_count
            total_stored += result.stored_count
        
        logger.info(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        logger.info(f"总爬取数据: {total_crawled} 条")
        logger.info(f"总存储数据: {total_stored} 条")
        
        # 获取执行统计
        stats = executor.get_execution_stats()
        logger.info(f"执行统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        logger.error(f"批量任务执行异常: {e}")
    
    finally:
        await executor.cleanup()


async def run_config_file_example():
    """从配置文件运行任务示例"""
    logger.info("=== 配置文件任务执行示例 ===")
    
    executor_config = TaskExecutorConfig()
    executor = TaskExecutor(executor_config)
    
    try:
        # 从配置文件执行任务
        config_file = Path("examples/task_config_example.json")
        
        if config_file.exists():
            result = await executor.execute_from_file(config_file)
            
            logger.info(f"配置文件任务执行完成:")
            logger.info(f"  任务ID: {result.task_id}")
            logger.info(f"  状态: {result.status.value}")
            logger.info(f"  爬取: {result.crawled_count} 条")
            logger.info(f"  清洗: {result.cleaned_count} 条")
            logger.info(f"  存储: {result.stored_count} 条")
            
        else:
            logger.warning(f"配置文件不存在: {config_file}")
            
    except Exception as e:
        logger.error(f"配置文件任务执行异常: {e}")
    
    finally:
        await executor.cleanup()


async def main():
    """主函数"""
    logger.info("TaskExecutor 示例程序启动")
    
    try:
        # 运行各种示例
        await run_single_task_example()
        await asyncio.sleep(2)
        
        await run_batch_tasks_example()
        await asyncio.sleep(2)
        
        await run_config_file_example()
        
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
    
    logger.info("示例程序结束")


if __name__ == "__main__":
    asyncio.run(main())
