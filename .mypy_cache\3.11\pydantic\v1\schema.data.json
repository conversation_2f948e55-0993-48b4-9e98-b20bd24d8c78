{".class": "MypyFile", "_fullname": "pydantic.v1.schema", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.AnyUrl", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedDecimal": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedDecimal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedFloat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedFrozenSet": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedFrozenSet", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedInt": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedInt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedList": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedList", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedSet": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedSet", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConstrainedStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.dataclasses.Dataclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EmailStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.EmailStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.FieldInfo", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ForwardRef": {".class": "SymbolTableNode", "cross_ref": "typing.ForwardRef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPv4Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Address", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPv4Interface": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Interface", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPv4Network": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Network", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPv6Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Address", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPv6Interface": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Interface", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPv6Network": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Network", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAPPING_LIKE_SHAPES": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.MAPPING_LIKE_SHAPES", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModelField": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.ModelField", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ROOT_KEY": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.ROOT_KEY", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_DEQUE": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_DEQUE", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_FROZENSET": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_FROZENSET", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_GENERIC": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_GENERIC", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_ITERABLE": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_ITERABLE", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_LIST": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_LIST", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_SEQUENCE": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_SEQUENCE", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_SET": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_SET", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_SINGLETON": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_SINGLETON", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_TUPLE": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_TUPLE", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SHAPE_TUPLE_ELLIPSIS": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.SHAPE_TUPLE_ELLIPSIS", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SecretBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.SecretBytes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SecretStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.SecretStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SkipField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.schema.SkipField", "name": "Ski<PERSON>Field", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.SkipField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.schema", "mro": ["pydantic.v1.schema.SkipField", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.schema.SkipField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["pydantic.v1.schema.SkipField", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SkipField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.schema.SkipField.message", "name": "message", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.schema.SkipField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.schema.SkipField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrictBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrictBytes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrictStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrictStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeModelOrEnum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.v1.schema.TypeModelOrEnum", "line": 92, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "TypeType", "item": "enum.Enum"}], "uses_pep604_syntax": false}}}, "TypeModelSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.v1.schema.TypeModelSet", "line": 93, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.schema.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.schema.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.schema.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.schema.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.schema.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.schema.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_apply_modify_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["modify_schema", "field", "field_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema._apply_modify_schema", "name": "_apply_modify_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["modify_schema", "field", "field_schema"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["pydantic.v1.fields.ModelField", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_apply_modify_schema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_map_types_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.schema._map_types_constraint", "name": "_map_types_constraint", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_numeric_types_attrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.schema._numeric_types_attrs", "name": "_numeric_types_attrs", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.type", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_str_types_attrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.schema._str_types_attrs", "name": "_str_types_attrs", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.type", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "add_field_type_to_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["field_type", "schema_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.add_field_type_to_schema", "name": "add_field_type_to_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["field_type", "schema_"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_field_type_to_schema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_literal_values": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.all_literal_values", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_hidden": true, "module_public": false}, "conbytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conbytes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "condecimal": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.condecimal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "confloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.confloat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "confrozenset": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.confrozenset", "kind": "Gdef", "module_hidden": true, "module_public": false}, "conint": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "conlist": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conlist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "conset": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conset", "kind": "Gdef", "module_hidden": true, "module_public": false}, "constr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.constr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "default_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.schema.default_prefix", "name": "default_prefix", "setter_type": null, "type": "builtins.str"}}, "default_ref_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.schema.default_ref_template", "name": "default_ref_template", "setter_type": null, "type": "builtins.str"}}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "encode_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dft"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.encode_default", "name": "encode_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dft"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "encode_default", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enum_process_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["enum", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.enum_process_schema", "name": "enum_process_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["enum", "field"], "arg_types": [{".class": "TypeType", "item": "enum.Enum"}, {".class": "UnionType", "items": ["pydantic.v1.fields.ModelField", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "enum_process_schema", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_class_to_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.schema.field_class_to_schema", "name": "field_class_to_schema", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "field_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 5, 5, 5], "arg_names": ["field", "by_alias", "model_name_map", "ref_prefix", "ref_template", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.field_schema", "name": "field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 5, 5, 5], "arg_names": ["field", "by_alias", "model_name_map", "ref_prefix", "ref_template", "known_models"], "arg_types": ["pydantic.v1.fields.ModelField", "builtins.bool", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_singleton_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 3], "arg_names": ["field", "by_alias", "model_name_map", "ref_template", "schema_overrides", "ref_prefix", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.field_singleton_schema", "name": "field_singleton_schema", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 3], "arg_names": ["field", "by_alias", "model_name_map", "ref_template", "schema_overrides", "ref_prefix", "known_models"], "arg_types": ["pydantic.v1.fields.ModelField", "builtins.bool", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_singleton_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_singleton_sub_fields_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 3], "arg_names": ["field", "by_alias", "model_name_map", "ref_template", "schema_overrides", "ref_prefix", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.field_singleton_sub_fields_schema", "name": "field_singleton_sub_fields_schema", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 3], "arg_names": ["field", "by_alias", "model_name_map", "ref_template", "schema_overrides", "ref_prefix", "known_models"], "arg_types": ["pydantic.v1.fields.ModelField", "builtins.bool", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_singleton_sub_fields_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_type_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 3], "arg_names": ["field", "by_alias", "model_name_map", "ref_template", "schema_overrides", "ref_prefix", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.field_type_schema", "name": "field_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 3], "arg_names": ["field", "by_alias", "model_name_map", "ref_template", "schema_overrides", "ref_prefix", "known_models"], "arg_types": ["pydantic.v1.fields.ModelField", "builtins.bool", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_type_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_annotation_from_field_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["annotation", "field_info", "field_name", "validate_assignment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_annotation_from_field_info", "name": "get_annotation_from_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["annotation", "field_info", "field_name", "validate_assignment"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.v1.fields.FieldInfo", "builtins.str", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotation_from_field_info", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_annotation_with_constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["annotation", "field_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_annotation_with_constraints", "name": "get_annotation_with_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["annotation", "field_info"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.v1.fields.FieldInfo"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotation_with_constraints", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_args": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.get_args", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_field_info_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["field", "schema_overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_field_info_schema", "name": "get_field_info_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["field", "schema_overrides"], "arg_types": ["pydantic.v1.fields.ModelField", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_field_info_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_field_schema_validations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_field_schema_validations", "name": "get_field_schema_validations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_field_schema_validations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flat_models_from_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["field", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_flat_models_from_field", "name": "get_flat_models_from_field", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["field", "known_models"], "arg_types": ["pydantic.v1.fields.ModelField", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_flat_models_from_field", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flat_models_from_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fields", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_flat_models_from_fields", "name": "get_flat_models_from_fields", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fields", "known_models"], "arg_types": [{".class": "Instance", "args": ["pydantic.v1.fields.ModelField"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_flat_models_from_fields", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flat_models_from_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["model", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_flat_models_from_model", "name": "get_flat_models_from_model", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "known_models"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_flat_models_from_model", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flat_models_from_models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_flat_models_from_models", "name": "get_flat_models_from_models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["models"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_flat_models_from_models", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_long_model_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_long_model_name", "name": "get_long_model_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_long_model_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.get_model", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_model_name_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["unique_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_model_name_map", "name": "get_model_name_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["unique_models"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_name_map", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.get_origin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_schema_ref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "ref_prefix", "ref_template", "schema_overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.get_schema_ref", "name": "get_schema_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "ref_prefix", "ref_template", "schema_overrides"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_schema_ref", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sub_types": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.get_sub_types", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_callable_type": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_callable_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.is_dataclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_literal_type": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_literal_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_namedtuple": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_namedtuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_none_type": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_none_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_union": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "json_scheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.schema.json_scheme", "name": "json_scheme", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.lenient_issubclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "model_process_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 5, 5, 5, 5], "arg_names": ["model", "by_alias", "model_name_map", "ref_prefix", "ref_template", "known_models", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.model_process_schema", "name": "model_process_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 5, 5, 5, 5], "arg_names": ["model", "by_alias", "model_name_map", "ref_prefix", "ref_template", "known_models", "field"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.bool", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pydantic.v1.fields.ModelField", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_process_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["model", "by_alias", "ref_prefix", "ref_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.model_schema", "name": "model_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["model", "by_alias", "ref_prefix", "ref_template"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_schema", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_type_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 3], "arg_names": ["model", "by_alias", "model_name_map", "ref_template", "ref_prefix", "known_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.model_type_schema", "name": "model_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 3], "arg_names": ["model", "by_alias", "model_name_map", "ref_template", "ref_prefix", "known_models"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "builtins.bool", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelOrEnum"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.schema.TypeModelSet"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_type_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multitypes_literal_field_for_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["values", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.multitypes_literal_field_for_schema", "name": "multitypes_literal_field_for_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["values", "field"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "pydantic.v1.fields.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "multitypes_literal_field_for_schema", "ret_type": "pydantic.v1.fields.ModelField", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalize_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.normalize_name", "name": "normalize_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "normalize_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "numeric_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.schema.numeric_types", "name": "numeric_types", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.ConvertibleToInt"}], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "int", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "base"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}, "typing.SupportsIndex"], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "int", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}, {".class": "CallableType", "arg_kinds": [1], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.ConvertibleToFloat"}], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "float", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["value", "context"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "decimal._DecimalNew"}, {".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "Decimal", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "pydantic_encoder": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.json.pydantic_encoder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_hidden": true, "module_public": false}, "schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["models", "by_alias", "title", "description", "ref_prefix", "ref_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.schema.schema", "name": "schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["models", "by_alias", "title", "description", "ref_prefix", "ref_template"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\v1\\schema.py"}