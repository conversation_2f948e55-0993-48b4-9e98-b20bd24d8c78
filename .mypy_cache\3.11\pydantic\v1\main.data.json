{".class": "MypyFile", "_fullname": "pydantic.v1.main", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ANNOTATED_FIELD_UNTOUCHED_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.ANNOTATED_FIELD_UNTOUCHED_TYPES", "name": "ANNOTATED_FIELD_UNTOUCHED_TYPES", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AbstractSetIntStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.AbstractSetIntStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyCallable": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.AnyCallable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyClassMethod": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.AnyClassMethod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.BaseConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.v1.utils.Representation"], "dataclass_transform_spec": null, "declared_metaclass": "pydantic.v1.main.ModelMetaclass", "defn": {".class": "ClassDef", "fullname": "pydantic.v1.main.BaseModel", "name": "BaseModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.BaseModel", "has_param_spec_type": false, "metaclass_type": "pydantic.v1.main.ModelMetaclass", "metadata": {"dataclass": {"attributes": [], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic.v1.main", "mro": ["pydantic.v1.main.BaseModel", "pydantic.v1.utils.Representation", "builtins.object"], "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.Config", "name": "Config", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "pydantic.v1.config.BaseConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__class_vars__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.main.BaseModel.__class_vars__", "name": "__class_vars__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.SetStr"}}}, "__config__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__config__", "name": "__config__", "setter_type": null, "type": {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}}}, "__custom_root_type__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__custom_root_type__", "name": "__custom_root_type__", "setter_type": null, "type": "builtins.bool"}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.v1.main.BaseModel.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.v1.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of BaseModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exclude_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__exclude_fields__", "name": "__exclude_fields__", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "__fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__fields__", "name": "__fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "pydantic.v1.fields.ModelField"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__fields_set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__fields_set__", "name": "__fields_set__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.SetStr"}}}, "__get_validators__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__get_validators__", "name": "__get_validators__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_validators__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.CallableGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.__get_validators__", "name": "__get_validators__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_validators__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.CallableGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getstate__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictAny"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__include_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__include_fields__", "name": "__include_fields__", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["__pydantic_self__", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["__pydantic_self__", "data"], "arg_types": ["pydantic.v1.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "__pydantic_self__"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.v1.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.TupleGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__json_encoder__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__json_encoder__", "name": "__json_encoder__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.v1.main.BaseModel.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.v1.main.BaseModel.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_root_validators__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.main.BaseModel.__post_root_validators__", "name": "__post_root_validators__", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__pre_root_validators__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.main.BaseModel.__pre_root_validators__", "name": "__pre_root_validators__", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__private_attributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.main.BaseModel.__private_attributes__", "name": "__private_attributes__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "pydantic.v1.fields.ModelPrivateAttr"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__repr_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__repr_args__", "name": "__repr_args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr_args__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.ReprArgs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__schema_cache__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__schema_cache__", "name": "__schema_cache__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictAny"}}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pydantic.v1.main.BaseModel.__setattr__", "name": "__setattr__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.__setattr__", "name": "__setattr__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["pydantic.v1.main.BaseModel", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictAny"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__setstate__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__signature__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.main.BaseModel.__signature__", "name": "__signature__", "setter_type": null, "type": "inspect.Signature"}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.v1.main.BaseModel.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__try_update_forward_refs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.__try_update_forward_refs__", "name": "__try_update_forward_refs__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__try_update_forward_refs__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.__try_update_forward_refs__", "name": "__try_update_forward_refs__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__try_update_forward_refs__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__validators__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.BaseModel.__validators__", "name": "__validators__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_calculate_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "include", "exclude", "exclude_unset", "update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel._calculate_keys", "name": "_calculate_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "include", "exclude", "exclude_unset", "update"], "arg_types": ["pydantic.v1.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_keys of BaseModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_and_set_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "values", "fields_set", "deep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.BaseModel._copy_and_set_values", "name": "_copy_and_set_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "values", "fields_set", "deep"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel._copy_and_set_values", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.SetStr"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_copy_and_set_values of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel._copy_and_set_values", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel._copy_and_set_values", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}, "_decompose_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel._decompose_class", "name": "_decompose_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel._decompose_class", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_decompose_class of BaseModel", "ret_type": "pydantic.v1.utils.GetterDict", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel._decompose_class", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel._decompose_class", "name": "_decompose_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel._decompose_class", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_decompose_class of BaseModel", "ret_type": "pydantic.v1.utils.GetterDict", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel._decompose_class", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}}, "_enforce_dict_if_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel._enforce_dict_if_root", "name": "_enforce_dict_if_root", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_enforce_dict_if_root of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel._enforce_dict_if_root", "name": "_enforce_dict_if_root", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_enforce_dict_if_root of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "v", "to_dict", "by_alias", "include", "exclude", "exclude_unset", "exclude_defaults", "exclude_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel._get_value", "name": "_get_value", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel._get_value", "name": "_get_value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_init_private_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel._init_private_attributes", "name": "_init_private_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_init_private_attributes of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "to_dict", "by_alias", "include", "exclude", "exclude_unset", "exclude_defaults", "exclude_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel._iter", "name": "_iter", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "to_dict", "by_alias", "include", "exclude", "exclude_unset", "exclude_defaults", "exclude_none"], "arg_types": ["pydantic.v1.main.BaseModel", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iter of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.TupleGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel.construct", "name": "construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.construct", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.SetStr"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.construct", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.construct", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.construct", "name": "construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.construct", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.SetStr"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.construct", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.construct", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "update", "deep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.BaseModel.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "update", "deep"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.copy", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.copy", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.copy", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}, "dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "by_alias", "skip_defaults", "exclude_unset", "exclude_defaults", "exclude_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.dict", "name": "dict", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "by_alias", "skip_defaults", "exclude_unset", "exclude_defaults", "exclude_none"], "arg_types": ["pydantic.v1.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dict of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_orm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel.from_orm", "name": "from_orm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.from_orm", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_orm of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.from_orm", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.from_orm", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.from_orm", "name": "from_orm", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.from_orm", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_orm of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.from_orm", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.from_orm", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}}, "json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "include", "exclude", "by_alias", "skip_defaults", "exclude_unset", "exclude_defaults", "exclude_none", "encoder", "models_as_dict", "dumps_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.json", "name": "json", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "include", "exclude", "by_alias", "skip_defaults", "exclude_unset", "exclude_defaults", "exclude_none", "encoder", "models_as_dict", "dumps_kwargs"], "arg_types": ["pydantic.v1.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "path", "content_type", "encoding", "proto", "allow_pickle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel.parse_file", "name": "parse_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "path", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_file", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["pydantic.v1.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_file of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_file", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_file", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.parse_file", "name": "parse_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "path", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_file", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["pydantic.v1.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_file of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_file", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_file", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}}, "parse_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel.parse_obj", "name": "parse_obj", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_obj", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_obj of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_obj", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_obj", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.parse_obj", "name": "parse_obj", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_obj", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_obj of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_obj", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_obj", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}}, "parse_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "b", "content_type", "encoding", "proto", "allow_pickle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel.parse_raw", "name": "parse_raw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "b", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_raw", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.StrBytes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["pydantic.v1.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_raw of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_raw", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_raw", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.parse_raw", "name": "parse_raw", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "b", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_raw", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.StrBytes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["pydantic.v1.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_raw of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_raw", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.parse_raw", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}}, "schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "by_alias", "ref_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.schema", "name": "schema", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "by_alias", "ref_template"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "builtins.bool", "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.schema", "name": "schema", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "by_alias", "ref_template"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "builtins.bool", "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "schema_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["cls", "by_alias", "ref_template", "dumps_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.schema_json", "name": "schema_json", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["cls", "by_alias", "ref_template", "dumps_kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema_json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.schema_json", "name": "schema_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["cls", "by_alias", "ref_template", "dumps_kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema_json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_forward_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.v1.main.BaseModel.update_forward_refs", "name": "update_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_forward_refs of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.update_forward_refs", "name": "update_forward_refs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_forward_refs of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.v1.main.BaseModel.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.validate", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.validate", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.validate", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.BaseModel.validate", "name": "validate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.validate", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.validate", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.BaseModel.validate", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CallableGenerator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.CallableGenerator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassAttribute": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.ClassAttribute", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConfigError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ConfigError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DUNDER_ATTRIBUTES": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.DUNDER_ATTRIBUTES", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DictAny": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.DictAny", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DictError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DictError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DictStrAny": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.DictStrAny", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ErrorWrapper": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.error_wrappers.ErrorWrapper", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Extra": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.Extra", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExtraError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ExtraError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "types.FunctionType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetterDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.GetterDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAPPING_LIKE_SHAPES": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.MAPPING_LIKE_SHAPES", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MappingIntStrAny": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.MappingIntStrAny", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MissingError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.MissingError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Model": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "name": "Model", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, "ModelField": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.ModelField", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModelMetaclass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["abc.ABCMeta"], "dataclass_transform_spec": {"eq_default": true, "field_specifiers": ["pydantic.v1.fields.Field"], "frozen_default": false, "kw_only_default": true, "order_default": false}, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.main.ModelMetaclass", "name": "ModelMetaclass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.ModelMetaclass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.main", "mro": ["pydantic.v1.main.ModelMetaclass", "abc.ABCMeta", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__instancecheck__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.main.ModelMetaclass.__instancecheck__", "name": "__instancecheck__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": ["pydantic.v1.main.ModelMetaclass", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__instancecheck__ of ModelMetaclass", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["mcs", "name", "bases", "namespace", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.v1.main.ModelMetaclass.__new__", "name": "__new__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.v1.main.ModelMetaclass.__new__", "name": "__new__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.ModelMetaclass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.main.ModelMetaclass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelOrDc": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ModelOrDc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModelPrivateAttr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.ModelPrivateAttr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PrivateAttr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.PrivateAttr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.parse.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PyObject": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.PyObject", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ROOT_KEY": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.ROOT_KEY", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReprArgs": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.ReprArgs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Representation": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.Representation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SetStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.SetStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Signature": {".class": "SymbolTableNode", "cross_ref": "inspect.Signature", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrBytes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TupleGenerator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.TupleGenerator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UNTOUCHED_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.main.UNTOUCHED_TYPES", "name": "UNTOUCHED_TYPES", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "Undefined": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Undefined", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.error_wrappers.ValidationError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValidatorGroup": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.ValidatorGroup", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValidatorListDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.ValidatorListDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValueItems": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.ValueItems", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.main.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.main.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.main.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.main.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.main.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.main.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.main.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_is_base_model_class_defined": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.main._is_base_model_class_defined", "name": "_is_base_model_class_defined", "setter_type": null, "type": "builtins.bool"}}, "_missing": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.main._missing", "name": "_missing", "setter_type": null, "type": "builtins.object"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.create_model", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "__slots__", "field_definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.v1.main.create_model", "name": "create_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "__slots__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.v1.main.create_model", "name": "create_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "NoneType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.main.create_model", "name": "create_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "NoneType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.v1.main.create_model", "name": "create_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.main.create_model", "name": "create_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "NoneType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.main.Model", "id": -1, "name": "Model", "namespace": "pydantic.v1.main.create_model#1", "upper_bound": "pydantic.v1.main.BaseModel", "values": [], "variance": 0}]}]}}}, "custom_pydantic_encoder": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.json.custom_pydantic_encoder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dataclass_transform": {".class": "SymbolTableNode", "cross_ref": "typing.dataclass_transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "default_ref_template": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.schema.default_ref_template", "kind": "Gdef", "module_hidden": true, "module_public": false}, "extract_root_validators": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.extract_root_validators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "extract_validators": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.extract_validators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generate_hash_function": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["frozen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.generate_hash_function", "name": "generate_hash_function", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["frozen"], "arg_types": ["builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_hash_function", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_model_signature": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.generate_model_signature", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_args": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.get_args", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.get_origin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "inherit_config": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.inherit_config", "kind": "Gdef", "module_hidden": true, "module_public": false}, "inherit_validators": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.inherit_validators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_classvar": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_classvar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_finalvar_with_default_val": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.is_finalvar_with_default_val", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_namedtuple": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_namedtuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_union": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_valid_field": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.is_valid_field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_valid_private_name": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.is_valid_private_name", "kind": "Gdef", "module_hidden": true, "module_public": false}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.lenient_issubclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "load_file": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.parse.load_file", "kind": "Gdef", "module_hidden": true, "module_public": false}, "load_str_bytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.parse.load_str_bytes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "model_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.schema.model_schema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef", "module_hidden": true, "module_public": false}, "object_setattr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.main.object_setattr", "name": "object_setattr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.object", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_hidden": true, "module_public": false}, "prepare_class": {".class": "SymbolTableNode", "cross_ref": "types.prepare_class", "kind": "Gdef", "module_hidden": true, "module_public": false}, "prepare_config": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.prepare_config", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pydantic_encoder": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.json.pydantic_encoder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "resolve_annotations": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.resolve_annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "resolve_bases": {".class": "SymbolTableNode", "cross_ref": "types.resolve_bases", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sequence_like": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.sequence_like", "kind": "Gdef", "module_hidden": true, "module_public": false}, "smart_deepcopy": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.smart_deepcopy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unique_list": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.unique_list", "kind": "Gdef", "module_hidden": true, "module_public": false}, "update_model_forward_refs": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.update_model_forward_refs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "validate_custom_root_type": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.validate_custom_root_type", "name": "validate_custom_root_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fields"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "pydantic.v1.fields.ModelField"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_custom_root_type", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_field_name": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.validate_field_name", "kind": "Gdef", "module_hidden": true, "module_public": false}, "validate_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["model", "input_data", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.main.validate_model", "name": "validate_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["model", "input_data", "cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_model", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.DictStrAny"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.SetStr"}, {".class": "UnionType", "items": ["pydantic.v1.error_wrappers.ValidationError", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\v1\\main.py"}