{".class": "MypyFile", "_fullname": "redis.commands", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AsyncCoreCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core.AsyncCoreCommands", "kind": "Gdef"}, "AsyncSentinelCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.sentinel.AsyncSentinelCommands", "kind": "Gdef"}, "CommandsParser": {".class": "SymbolTableNode", "cross_ref": "redis.commands.parser.CommandsParser", "kind": "Gdef"}, "CoreCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core.CoreCommands", "kind": "Gdef"}, "RedisClusterCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.cluster.RedisClusterCommands", "kind": "Gdef"}, "RedisModuleCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.redismodules.RedisModuleCommands", "kind": "Gdef"}, "SentinelCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.sentinel.SentinelCommands", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "redis.commands.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "list_or_args": {".class": "SymbolTableNode", "cross_ref": "redis.commands.helpers.list_or_args", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\redis-stubs\\commands\\__init__.pyi"}