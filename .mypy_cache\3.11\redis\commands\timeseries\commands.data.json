{".class": "MypyFile", "_fullname": "redis.commands.timeseries.commands", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ADD_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.ADD_CMD", "name": "ADD_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.ADD"}}}, "ALTER_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.ALTER_CMD", "name": "ALTER_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.ALTER"}}}, "CREATERULE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.CREATERULE_CMD", "name": "CREATERULE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.CREATERULE"}}}, "CREATE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.CREATE_CMD", "name": "CREATE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.CREATE"}}}, "DECRBY_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.DECRBY_CMD", "name": "DECRBY_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.DECRBY"}}}, "DELETERULE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.DELETERULE_CMD", "name": "DELETERULE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.DELETERULE"}}}, "DEL_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.DEL_CMD", "name": "DEL_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.DEL"}}}, "GET_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.GET_CMD", "name": "GET_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.GET"}}}, "INCRBY_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.INCRBY_CMD", "name": "INCRBY_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.INCRBY"}}}, "INFO_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.INFO_CMD", "name": "INFO_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.INFO"}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MADD_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.MADD_CMD", "name": "MADD_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.MADD"}}}, "MGET_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.MGET_CMD", "name": "MGET_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.MGET"}}}, "MRANGE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.MRANGE_CMD", "name": "MRANGE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.MRANGE"}}}, "MREVRANGE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.MREVRANGE_CMD", "name": "MREVRANGE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.MREVRANGE"}}}, "QUERYINDEX_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.QUERYINDEX_CMD", "name": "QUERYINDEX_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.QUERYINDEX"}}}, "RANGE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.RANGE_CMD", "name": "RANGE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.RANGE"}}}, "REVRANGE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.REVRANGE_CMD", "name": "REVRANGE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "TS.REVRANGE"}}}, "TimeSeriesCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands", "name": "TimeSeriesCommands", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.timeseries.commands", "mro": ["redis.commands.timeseries.commands.TimeSeriesCommands", "builtins.object"], "names": {".class": "SymbolTable", "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "timestamp", "value", "retention_msecs", "uncompressed", "labels", "chunk_size", "duplicate_policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "timestamp", "value", "retention_msecs", "uncompressed", "labels", "chunk_size", "duplicate_policy"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "key", "retention_msecs", "labels", "chunk_size", "duplicate_policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.alter", "name": "alter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "key", "retention_msecs", "labels", "chunk_size", "duplicate_policy"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "alter of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "retention_msecs", "uncompressed", "labels", "chunk_size", "duplicate_policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "retention_msecs", "uncompressed", "labels", "chunk_size", "duplicate_policy"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "createrule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "source_key", "dest_key", "aggregation_type", "bucket_size_msec", "align_timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.createrule", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "source_key", "dest_key", "aggregation_type", "bucket_size_msec", "align_timestamp"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create<PERSON>le of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decrby": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "value", "timestamp", "retention_msecs", "uncompressed", "labels", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.decrby", "name": "dec<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "value", "timestamp", "retention_msecs", "uncompressed", "labels", "chunk_size"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decrby of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "from_time", "to_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.delete", "name": "delete", "type": null}}, "deleterule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_key", "dest_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.deleterule", "name": "deleterule", "type": null}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "latest"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "latest"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "incrby": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "value", "timestamp", "retention_msecs", "uncompressed", "labels", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.incrby", "name": "incrby", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "value", "timestamp", "retention_msecs", "uncompressed", "labels", "chunk_size"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "incrby of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.info", "name": "info", "type": null}}, "madd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ktv_tuples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.madd", "name": "madd", "type": null}}, "mget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "filters", "with_labels", "select_labels", "latest"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.mget", "name": "mget", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "filters", "with_labels", "select_labels", "latest"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mget of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mrange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "from_time", "to_time", "filters", "count", "aggregation_type", "bucket_size_msec", "with_labels", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "groupby", "reduce", "select_labels", "align", "latest", "bucket_timestamp", "empty"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.mrange", "name": "m<PERSON>e", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "from_time", "to_time", "filters", "count", "aggregation_type", "bucket_size_msec", "with_labels", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "groupby", "reduce", "select_labels", "align", "latest", "bucket_timestamp", "empty"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mrange of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mrevrange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "from_time", "to_time", "filters", "count", "aggregation_type", "bucket_size_msec", "with_labels", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "groupby", "reduce", "select_labels", "align", "latest", "bucket_timestamp", "empty"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.mrevrange", "name": "mrevrange", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "from_time", "to_time", "filters", "count", "aggregation_type", "bucket_size_msec", "with_labels", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "groupby", "reduce", "select_labels", "align", "latest", "bucket_timestamp", "empty"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mrevrange of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "queryindex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.queryindex", "name": "queryindex", "type": null}}, "range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "from_time", "to_time", "count", "aggregation_type", "bucket_size_msec", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "align", "latest", "bucket_timestamp", "empty"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.range", "name": "range", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "from_time", "to_time", "count", "aggregation_type", "bucket_size_msec", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "align", "latest", "bucket_timestamp", "empty"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "range of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "revrange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "from_time", "to_time", "count", "aggregation_type", "bucket_size_msec", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "align", "latest", "bucket_timestamp", "empty"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.revrange", "name": "revrange", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key", "from_time", "to_time", "count", "aggregation_type", "bucket_size_msec", "filter_by_ts", "filter_by_min_value", "filter_by_max_value", "align", "latest", "bucket_timestamp", "empty"], "arg_types": ["redis.commands.timeseries.commands.TimeSeriesCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.timeseries.commands._Key"}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "revrange of TimeSeriesCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.timeseries.commands.TimeSeriesCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.timeseries.commands.TimeSeriesCommands", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.commands.timeseries.commands._Key", "line": 4, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.timeseries.commands.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\redis-stubs\\commands\\timeseries\\commands.pyi"}