{".class": "MypyFile", "_fullname": "redis.asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AuthenticationError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.AuthenticationError", "kind": "Gdef"}, "AuthenticationWrongNumberOfArgsError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.AuthenticationWrongNumberOfArgsError", "kind": "Gdef"}, "BlockingConnectionPool": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.BlockingConnectionPool", "kind": "Gdef"}, "BusyLoadingError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.BusyLoadingError", "kind": "Gdef"}, "ChildDeadlockedError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.ChildDeadlockedError", "kind": "Gdef"}, "CommandsParser": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.parser.CommandsParser", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.Connection", "kind": "Gdef"}, "ConnectionError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.ConnectionError", "kind": "Gdef"}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.ConnectionPool", "kind": "Gdef"}, "DataError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.DataError", "kind": "Gdef"}, "InvalidResponse": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.InvalidResponse", "kind": "Gdef"}, "PubSubError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.PubSubError", "kind": "Gdef"}, "ReadOnlyError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.ReadOnlyError", "kind": "Gdef"}, "Redis": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.client.Redis", "kind": "Gdef"}, "RedisCluster": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.cluster.RedisCluster", "kind": "Gdef"}, "RedisError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.RedisError", "kind": "Gdef"}, "ResponseError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.ResponseError", "kind": "Gdef"}, "SSLConnection": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.SSLConnection", "kind": "Gdef"}, "Sentinel": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.sentinel.Sentinel", "kind": "Gdef"}, "SentinelConnectionPool": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.sentinel.SentinelConnectionPool", "kind": "Gdef"}, "SentinelManagedConnection": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.sentinel.SentinelManagedConnection", "kind": "Gdef"}, "SentinelManagedSSLConnection": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.sentinel.SentinelManagedSSLConnection", "kind": "Gdef"}, "StrictRedis": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.client.StrictRedis", "kind": "Gdef"}, "TimeoutError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.TimeoutError", "kind": "Gdef"}, "UnixDomainSocketConnection": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.UnixDomainSocketConnection", "kind": "Gdef"}, "WatchError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.WatchError", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "redis.asyncio.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "default_backoff": {".class": "SymbolTableNode", "cross_ref": "redis.backoff.default_backoff", "kind": "Gdef"}, "from_url": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.utils.from_url", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\redis-stubs\\asyncio\\__init__.pyi"}