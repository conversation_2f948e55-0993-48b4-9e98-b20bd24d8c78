{".class": "MypyFile", "_fullname": "pydantic.v1.dataclasses", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.BaseConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CallableGenerator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.CallableGenerator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassAttribute": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.ClassAttribute", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.ConfigDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dataclass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.dataclasses.Dataclass", "name": "Dataclass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses.Dataclass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.dataclasses", "mro": ["pydantic.v1.dataclasses.Dataclass", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__dataclass_params__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__dataclass_params__", "name": "__dataclass_params__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__get_validators__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_mypy_only"], "fullname": "pydantic.v1.dataclasses.Dataclass.__get_validators__", "name": "__get_validators__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_validators__ of Dataclass", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.CallableGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.dataclasses.Dataclass.__get_validators__", "name": "__get_validators__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_validators__ of Dataclass", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.CallableGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self", "is_mypy_only"], "fullname": "pydantic.v1.dataclasses.Dataclass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.v1.dataclasses.Dataclass", "builtins.object", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Dataclass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__post_init__", "name": "__post_init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__post_init_post_parse__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__post_init_post_parse__", "name": "__post_init_post_parse__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__pydantic_has_field_info_default__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__pydantic_has_field_info_default__", "name": "__pydantic_has_field_info_default__", "setter_type": null, "type": "builtins.bool"}}, "__pydantic_initialised__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__pydantic_initialised__", "name": "__pydantic_initialised__", "setter_type": null, "type": "builtins.bool"}}, "__pydantic_model__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__pydantic_model__", "name": "__pydantic_model__", "setter_type": null, "type": {".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}}}, "__pydantic_run_validation__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__pydantic_run_validation__", "name": "__pydantic_run_validation__", "setter_type": null, "type": "builtins.bool"}}, "__pydantic_validate_values__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.v1.dataclasses.Dataclass.__pydantic_validate_values__", "name": "__pydantic_validate_values__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.v1.dataclasses.Dataclass"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__validate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "v"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_mypy_only"], "fullname": "pydantic.v1.dataclasses.Dataclass.__validate__", "name": "__validate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "v"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.Dataclass.__validate__", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__validate__ of Dataclass", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.Dataclass.__validate__", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.Dataclass.__validate__", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.v1.dataclasses.Dataclass.__validate__", "name": "__validate__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "v"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.Dataclass.__validate__", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__validate__ of Dataclass", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.Dataclass.__validate__", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.Dataclass.__validate__", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.Dataclass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataclassClassOrWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.v1.dataclasses.DataclassClassOrWrapper", "line": 64, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}, "pydantic.v1.dataclasses.DataclassProxy"], "uses_pep604_syntax": false}}}, "DataclassProxy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.dataclasses.DataclassProxy", "name": "DataclassProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses.DataclassProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.dataclasses", "mro": ["pydantic.v1.dataclasses.DataclassProxy", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.v1.dataclasses.DataclassProxy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of DataclassProxy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.dataclasses.DataclassProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__copy__ of DataclassProxy", "ret_type": "pydantic.v1.dataclasses.DataclassProxy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "arg_types": ["pydantic.v1.dataclasses.DataclassProxy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__deepcopy__ of DataclassProxy", "ret_type": "pydantic.v1.dataclasses.DataclassProxy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.v1.dataclasses.DataclassProxy", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of DataclassProxy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dc_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dc_cls"], "arg_types": ["pydantic.v1.dataclasses.DataclassProxy", {".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DataclassProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__instancecheck__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__instancecheck__", "name": "__instancecheck__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": ["pydantic.v1.dataclasses.DataclassProxy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__instancecheck__ of DataclassProxy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__setattr__", "name": "__setattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["pydantic.v1.dataclasses.DataclassProxy", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__setattr__ of DataclassProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.v1.dataclasses.DataclassProxy.__slots__", "name": "__slots__", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.dataclasses.DataclassProxy", "values": [], "variance": 0}, "slots": ["__dataclass__"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataclassT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "name": "DataclassT", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, "DataclassTypeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DataclassTypeError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Extra": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.Extra", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.FieldInfo", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoArgAnyCallable": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.NoArgAnyCallable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Required": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Required", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Undefined": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Undefined", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.error_wrappers.ValidationError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.dataclasses.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.dataclasses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.dataclasses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.dataclasses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.dataclasses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.dataclasses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.dataclasses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_add_pydantic_validation_attributes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["dc_cls", "config", "validate_on_init", "dc_cls_doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses._add_pydantic_validation_attributes", "name": "_add_pydantic_validation_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["dc_cls", "config", "validate_on_init", "dc_cls_doc"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}, {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}, "builtins.bool", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_pydantic_validation_attributes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dataclass_validate_assignment_setattr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses._dataclass_validate_assignment_setattr", "name": "_dataclass_validate_assignment_setattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["pydantic.v1.dataclasses.Dataclass", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_dataclass_validate_assignment_setattr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dataclass_validate_values": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses._dataclass_validate_values", "name": "_dataclass_validate_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.dataclasses.Dataclass"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_dataclass_validate_values", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_validators": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses._get_validators", "name": "_get_validators", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.CallableGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_field_cached_property": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses._is_field_cached_property", "name": "_is_field_cached_property", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "k"], "arg_types": ["pydantic.v1.dataclasses.Dataclass", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_field_cached_property", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_dataclass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "v"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses._validate_dataclass", "name": "_validate_dataclass", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "v"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses._validate_dataclass", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_dataclass", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses._validate_dataclass", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses._validate_dataclass", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}]}}}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef", "module_hidden": true, "module_public": false}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.create_model", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_pydantic_model_from_dataclass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["dc_cls", "config", "dc_cls_doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses.create_pydantic_model_from_dataclass", "name": "create_pydantic_model_from_dataclass", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["dc_cls", "config", "dc_cls_doc"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_pydantic_model_from_dataclass", "ret_type": {".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses.dataclass", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "dataclass_transform_spec": {"eq_default": true, "field_specifiers": ["dataclasses.field", "pydantic.v1.fields.Field"], "frozen_default": false, "kw_only_default": false, "order_default": false}, "deprecated": null, "flags": ["is_decorated"], "fullname": "pydantic.v1.dataclasses.dataclass", "name": "dataclass", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.dataclasses.dataclass", "name": "dataclass", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "dataclass_transform_spec": {"eq_default": true, "field_specifiers": ["dataclasses.field", "pydantic.v1.fields.Field"], "frozen_default": false, "kw_only_default": false, "order_default": false}, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.v1.dataclasses.dataclass", "name": "dataclass", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.dataclasses.dataclass", "name": "dataclass", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "dataclass_transform_spec": {"eq_default": true, "field_specifiers": ["dataclasses.field", "pydantic.v1.fields.Field"], "frozen_default": false, "kw_only_default": false, "order_default": false}, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.v1.dataclasses.dataclass", "name": "dataclass", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.dataclasses.dataclass", "name": "dataclass", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "init", "repr", "eq", "order", "unsafe_hash", "frozen", "config", "validate_on_init", "use_proxy", "kw_only"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.config.ConfigDict"}, {".class": "TypeType", "item": "builtins.object"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dataclass", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.dataclasses.DataclassClassOrWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses._T", "id": -1, "name": "_T", "namespace": "pydantic.v1.dataclasses.dataclass#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "dataclass_transform": {".class": "SymbolTableNode", "cross_ref": "typing.dataclass_transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_hidden": true, "module_public": false}, "gather_all_validators": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.gather_all_validators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_config": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.get_config", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_builtin_dataclass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses.is_builtin_dataclass", "name": "is_builtin_dataclass", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_builtin_dataclass", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_dataclass_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["dc_cls", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.dataclasses.make_dataclass_validator", "name": "make_dataclass_validator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["dc_cls", "config"], "arg_types": [{".class": "TypeType", "item": "pydantic.v1.dataclasses.Dataclass"}, {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_dataclass_validator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.CallableGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "set_validation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pydantic.v1.dataclasses.set_validation", "name": "set_validation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.set_validation", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_validation", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.set_validation", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.set_validation", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.dataclasses.set_validation", "name": "set_validation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.set_validation", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_validation", "ret_type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.set_validation", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.dataclasses.DataclassT", "id": -1, "name": "DataclassT", "namespace": "pydantic.v1.dataclasses.set_validation", "upper_bound": "pydantic.v1.dataclasses.Dataclass", "values": [], "variance": 0}]}}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "validate_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.validate_model", "kind": "Gdef", "module_hidden": true, "module_public": false}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py"}