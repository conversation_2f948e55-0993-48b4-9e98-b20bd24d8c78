{".class": "MypyFile", "_fullname": "pydantic._internal._dataclasses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArgsKwargs": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.ArgsKwargs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.ConfigDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GenerateSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generate_schema.GenerateSchema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "InvalidSchemaError": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generate_schema.InvalidSchemaError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LazyClassAttribute": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.LazyClassAttribute", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NsResolver": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.NsResolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PluggableSchemaValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic.plugin._schema_validator.PluggableSchemaValidator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PydanticDataclass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__dataclass_fields__", 1], ["__pydantic_complete__", 1], ["__pydantic_config__", 1], ["__pydantic_core_schema__", 1], ["__pydantic_decorators__", 1], ["__pydantic_fields__", 1], ["__pydantic_fields_complete__", 2], ["__pydantic_serializer__", 1], ["__pydantic_validator__", 1]], "alt_promote": null, "bases": ["_typeshed.DataclassInstance"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._dataclasses.PydanticDataclass", "name": "PydanticDataclass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._dataclasses", "mro": ["pydantic._internal._dataclasses.PydanticDataclass", "_typeshed.DataclassInstance", "builtins.object"], "names": {".class": "SymbolTable", "__pydantic_complete__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_complete__", "name": "__pydantic_complete__", "setter_type": null, "type": "builtins.bool"}}, "__pydantic_config__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_config__", "name": "__pydantic_config__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}}}, "__pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_core_schema__", "name": "__pydantic_core_schema__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}}}, "__pydantic_decorators__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_decorators__", "name": "__pydantic_decorators__", "setter_type": null, "type": "pydantic._internal._decorators.DecoratorInfos"}}, "__pydantic_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_fields__", "name": "__pydantic_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__pydantic_fields_complete__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body", "is_trivial_self", "is_mypy_only"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_fields_complete__", "name": "__pydantic_fields_complete__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic._internal._dataclasses.PydanticDataclass"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__pydantic_fields_complete__ of PydanticDataclass", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_fields_complete__", "name": "__pydantic_fields_complete__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic._internal._dataclasses.PydanticDataclass"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__pydantic_fields_complete__ of PydanticDataclass", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__pydantic_serializer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_serializer__", "name": "__pydantic_serializer__", "setter_type": null, "type": "pydantic_core._pydantic_core.SchemaSerializer"}}, "__pydantic_validator__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_abstract_var", "is_ready"], "fullname": "pydantic._internal._dataclasses.PydanticDataclass.__pydantic_validator__", "name": "__pydantic_validator__", "setter_type": null, "type": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator"], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._dataclasses.PydanticDataclass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._dataclasses.PydanticDataclass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticDeprecatedSince20": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince20", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PydanticUndefinedAnnotation": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUndefinedAnnotation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SchemaSerializer": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaSerializer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SchemaValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaValidator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StandardDataclass": {".class": "SymbolTableNode", "cross_ref": "_typeshed.DataclassInstance", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._dataclasses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._dataclasses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._dataclasses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._dataclasses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._dataclasses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._dataclasses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_config": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "collect_dataclass_fields": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields.collect_dataclass_fields", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complete_dataclass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["cls", "config_wrapper", "raise_errors", "ns_resolver", "_force_build"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._dataclasses.complete_dataclass", "name": "complete_dataclass", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["cls", "config_wrapper", "raise_errors", "ns_resolver", "_force_build"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "pydantic._internal._config.ConfigWrapper", "builtins.bool", {".class": "UnionType", "items": ["pydantic._internal._namespace_utils.NsResolver", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "complete_dataclass", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_schema_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.plugin._schema_validator.create_schema_validator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generate_pydantic_signature": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._signature.generate_pydantic_signature", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_standard_typevars_map": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.get_standard_typevars_map", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_builtin_dataclass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._dataclasses.is_builtin_dataclass", "name": "is_builtin_dataclass", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_builtin_dataclass", "ret_type": "builtins.bool", "type_guard": {".class": "TypeType", "item": "_typeshed.DataclassInstance"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_hidden": true, "module_public": false}, "set_dataclass_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "ns_resolver", "config_wrapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._dataclasses.set_dataclass_fields", "name": "set_dataclass_fields", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "ns_resolver", "config_wrapper"], "arg_types": [{".class": "TypeType", "item": "_typeshed.DataclassInstance"}, {".class": "UnionType", "items": ["pydantic._internal._namespace_utils.NsResolver", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pydantic._internal._config.ConfigWrapper", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_dataclass_fields", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_dataclass_mocks": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._mock_val_ser.set_dataclass_mocks", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_hidden": true, "module_public": false}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py"}