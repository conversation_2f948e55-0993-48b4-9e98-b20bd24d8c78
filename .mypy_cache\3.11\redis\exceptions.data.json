{".class": "MypyFile", "_fullname": "redis.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AskError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.AskError", "name": "AskError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.AskError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.AskError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.exceptions.AskError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resp"], "arg_types": ["redis.exceptions.AskError", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AskError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.AskError.args", "name": "args", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.AskError.host", "name": "host", "setter_type": null, "type": "builtins.str"}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.AskError.message", "name": "message", "setter_type": null, "type": "builtins.str"}}, "node_addr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.AskError.node_addr", "name": "node_addr", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.AskError.port", "name": "port", "setter_type": null, "type": "builtins.int"}}, "slot_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.AskError.slot_id", "name": "slot_id", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.AskError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.AskError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AuthenticationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.AuthenticationError", "name": "AuthenticationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.AuthenticationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.AuthenticationError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.AuthenticationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.AuthenticationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AuthenticationWrongNumberOfArgsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.AuthenticationWrongNumberOfArgsError", "name": "AuthenticationWrongNumberOfArgsError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.AuthenticationWrongNumberOfArgsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.AuthenticationWrongNumberOfArgsError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.AuthenticationWrongNumberOfArgsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.AuthenticationWrongNumberOfArgsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AuthorizationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.AuthorizationError", "name": "AuthorizationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.AuthorizationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.AuthorizationError", "redis.exceptions.ConnectionError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.AuthorizationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.AuthorizationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BusyLoadingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.BusyLoadingError", "name": "BusyLoadingError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.BusyLoadingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.BusyLoadingError", "redis.exceptions.ConnectionError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.BusyLoadingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.BusyLoadingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChildDeadlockedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ChildDeadlockedError", "name": "ChildDeadlockedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ChildDeadlockedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ChildDeadlockedError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ChildDeadlockedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ChildDeadlockedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClusterCrossSlotError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ClusterCrossSlotError", "name": "ClusterCrossSlotError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ClusterCrossSlotError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ClusterCrossSlotError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ClusterCrossSlotError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ClusterCrossSlotError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClusterDownError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ClusterError", "redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ClusterDownError", "name": "ClusterDownError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ClusterDownError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ClusterDownError", "redis.exceptions.ClusterError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.exceptions.ClusterDownError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resp"], "arg_types": ["redis.exceptions.ClusterDownError", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ClusterDownError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.ClusterDownError.args", "name": "args", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.exceptions.ClusterDownError.message", "name": "message", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ClusterDownError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ClusterDownError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClusterError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ClusterError", "name": "ClusterError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ClusterError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ClusterError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ClusterError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ClusterError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ConnectionError", "name": "ConnectionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ConnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ConnectionError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ConnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ConnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.DataError", "name": "DataError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.DataError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.DataError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.DataError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.DataError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExecAbortError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ExecAbortError", "name": "ExecAbortError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ExecAbortError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ExecAbortError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ExecAbortError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ExecAbortError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.InvalidResponse", "name": "InvalidResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.InvalidResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.InvalidResponse", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.InvalidResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.InvalidResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LockError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.LockError", "name": "LockError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.LockError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.LockError", "redis.exceptions.RedisError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.LockError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.LockError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LockNotOwnedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.LockError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.LockNotOwnedError", "name": "LockNotOwnedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.LockNotOwnedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.LockNotOwnedError", "redis.exceptions.LockError", "redis.exceptions.RedisError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.LockNotOwnedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.LockNotOwnedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MasterDownError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ClusterDownError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.MasterDownError", "name": "MasterDownError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.MasterDownError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.MasterDownError", "redis.exceptions.ClusterDownError", "redis.exceptions.ClusterError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.MasterDownError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.MasterDownError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModuleError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ModuleError", "name": "<PERSON><PERSON>le<PERSON><PERSON>r", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ModuleError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ModuleError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ModuleError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ModuleError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MovedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.AskError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.MovedError", "name": "MovedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.MovedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.MovedError", "redis.exceptions.AskError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.MovedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.MovedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoPermissionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.NoPermissionError", "name": "NoPermissionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.NoPermissionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.NoPermissionError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.NoPermissionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.NoPermissionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoScriptError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.NoScriptError", "name": "NoScriptError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.NoScriptError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.NoScriptError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.NoScriptError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.NoScriptError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PubSubError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.PubSubError", "name": "PubSubError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.PubSubError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.PubSubError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.PubSubError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.PubSubError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadOnlyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ReadOnlyError", "name": "ReadOnlyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ReadOnlyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ReadOnlyError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ReadOnlyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ReadOnlyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RedisClusterException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.RedisClusterException", "name": "RedisClusterException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.RedisClusterException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.RedisClusterException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.RedisClusterException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.RedisClusterException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RedisError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.RedisError", "name": "RedisError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.RedisError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.RedisError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.RedisError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.ResponseError", "name": "ResponseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.ResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.ResponseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.ResponseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SlotNotCoveredError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisClusterException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.SlotNotCoveredError", "name": "SlotNotCoveredError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.SlotNotCoveredError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.SlotNotCoveredError", "redis.exceptions.RedisClusterException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.SlotNotCoveredError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.SlotNotCoveredError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.TimeoutError", "name": "TimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.TimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.TimeoutError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.TimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.TimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TryAgainError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ResponseError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.TryAgainError", "name": "TryAgainError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.TryAgainError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.TryAgainError", "redis.exceptions.ResponseError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.TryAgainError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.TryAgainError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WatchError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.RedisError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.exceptions.WatchError", "name": "WatchError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.exceptions.WatchError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.exceptions", "mro": ["redis.exceptions.WatchError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.exceptions.WatchError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.exceptions.WatchError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.exceptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.exceptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.exceptions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.exceptions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.exceptions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.exceptions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\redis-stubs\\exceptions.pyi"}