{".class": "MypyFile", "_fullname": "pydantic._internal._core_utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._core_utils.AnyFunctionSchema", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.AfterValidatorFunctionSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BeforeValidatorFunctionSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapValidatorFunctionSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.PlainValidatorFunctionSchema"}], "uses_pep604_syntax": false}}}, "Console": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pydantic._internal._core_utils.Console", "name": "<PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "pydantic._internal._core_utils.Console", "source_any": null, "type_of_any": 3}}}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CoreSchemaField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._core_utils.CoreSchemaField", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "uses_pep604_syntax": false}}}, "CoreSchemaOrField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._core_utils.CoreSchemaOrField", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaField"}], "uses_pep604_syntax": false}}}, "FunctionSchemaWithInnerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._core_utils.FunctionSchemaWithInnerSchema", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.AfterValidatorFunctionSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BeforeValidatorFunctionSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapValidatorFunctionSchema"}], "uses_pep604_syntax": false}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CORE_SCHEMA_FIELD_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._core_utils._CORE_SCHEMA_FIELD_TYPES", "name": "_CORE_SCHEMA_FIELD_TYPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_FUNCTION_WITH_INNER_SCHEMA_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._core_utils._FUNCTION_WITH_INNER_SCHEMA_TYPES", "name": "_FUNCTION_WITH_INNER_SCHEMA_TYPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_LIST_LIKE_SCHEMA_WITH_ITEMS_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._core_utils._LIST_LIKE_SCHEMA_WITH_ITEMS_TYPES", "name": "_LIST_LIKE_SCHEMA_WITH_ITEMS_TYPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._core_utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_clean_schema_for_pretty_print": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "strip_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils._clean_schema_for_pretty_print", "name": "_clean_schema_for_pretty_print", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "strip_metadata"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_clean_schema_for_pretty_print", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_repr": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._repr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_validate_core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.validate_core_schema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_ref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.get_ref", "name": "get_ref", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_ref", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_type_ref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["type_", "args_override"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.get_type_ref", "name": "get_type_ref", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["type_", "args_override"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_type_ref", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_core_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.is_core_schema", "name": "is_core_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_core_schema", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_core_schema_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.is_core_schema_field", "name": "is_core_schema_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_core_schema_field", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaField"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_function_with_inner_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.is_function_with_inner_schema", "name": "is_function_with_inner_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_function_with_inner_schema", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.FunctionSchemaWithInnerSchema"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_generic_alias": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra.is_generic_alias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_list_like_schema_with_items_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.is_list_like_schema_with_items_schema", "name": "is_list_like_schema_with_items_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_list_like_schema_with_items_schema", "ret_type": "builtins.bool", "type_guard": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ListSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SetSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FrozenSetSchema"}], "uses_pep604_syntax": true}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._core_utils.pps", "name": "pps", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["val", "console", "max_depth", "strip_metadata"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "pydantic._internal._core_utils.Console", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pretty_print_core_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["val", "console", "max_depth", "strip_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.pretty_print_core_schema", "name": "pretty_print_core_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["val", "console", "max_depth", "strip_metadata"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "pydantic._internal._core_utils.Console", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pretty_print_core_schema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing_objects": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.typing_objects", "kind": "Gdef", "module_hidden": true, "module_public": false}, "validate_core_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._core_utils.validate_core_schema", "name": "validate_core_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_core_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py"}