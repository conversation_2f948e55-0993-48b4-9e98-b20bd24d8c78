{"data_mtime": 1752386759, "dep_lines": [15, 24, 16, 10, 13, 7, 8, 9, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["src.data_trans.config.settings", "src.data_trans.queue.task_handlers", "src.data_trans.queue", "unittest.mock", "redis.asyncio", "asyncio", "uuid", "datetime", "redis", "builtins", "_frozen_importlib", "abc", "enum", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "redis.asyncio.client", "redis.asyncio.connection", "redis.asyncio.retry", "redis.client", "redis.commands", "redis.commands.core", "redis.commands.redismodules", "redis.commands.sentinel", "redis.credentials", "redis.exceptions", "src", "src.data_trans.config", "src.data_trans.queue.task_status", "typing", "typing_extensions"], "hash": "3267383b65c7aa08f36888228110d5aee6a01347", "id": "tests.test_queue_system", "ignore_all": false, "interface_hash": "be7e75a45ccc992e81ca7d0f9849b125f76d50f0", "mtime": 1752387069, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "tests\\test_queue_system.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 10328, "suppressed": ["pytest"], "version_id": "1.16.1"}