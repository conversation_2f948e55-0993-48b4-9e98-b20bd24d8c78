"""
队列管理器

统一管理任务队列、消费者组和后台任务。
"""

import asyncio
import logging
from typing import Any, Callable, Dict, List, Optional

import redis.asyncio as redis

from ..config.settings import RedisConfig
from .consumer_group import ConsumerGroup
from .task_queue import TaskQueue
from .task_status import TaskPriority, TaskState, TaskStatus

logger = logging.getLogger(__name__)


class QueueManager:
    """队列管理器"""

    def __init__(self, redis_config: RedisConfig):
        """初始化队列管理器

        Args:
            redis_config: Redis配置
        """
        self.redis_config = redis_config
        self.redis_client: Optional[redis.Redis] = None
        self.task_queue: Optional[TaskQueue] = None
        self.consumer_groups: Dict[str, ConsumerGroup] = {}
        self.task_handlers: Dict[str, Callable] = {}
        self._background_tasks: List[asyncio.Task] = []
        self._running = False

    async def connect(self) -> None:
        """连接Redis"""
        try:
            # 创建Redis连接池
            pool = redis.ConnectionPool(
                host=self.redis_config.host,
                port=self.redis_config.port,
                password=self.redis_config.password,
                db=self.redis_config.queue_database,
                max_connections=self.redis_config.max_connections,
                socket_timeout=self.redis_config.socket_timeout,
                socket_connect_timeout=self.redis_config.socket_connect_timeout,
                decode_responses=False,  # 保持原始字节数据
            )

            self.redis_client = redis.Redis(connection_pool=pool)

            # 测试连接
            await self.redis_client.ping()

            # 创建任务队列
            self.task_queue = TaskQueue(self.redis_client)

            logger.info("队列管理器连接成功")

        except Exception as e:
            logger.error(f"队列管理器连接失败: {e}")
            raise

    async def disconnect(self) -> None:
        """断开连接"""
        try:
            # 停止后台任务
            await self.stop_background_tasks()

            # 关闭Redis连接
            if self.redis_client:
                await self.redis_client.close()

            logger.info("队列管理器已断开连接")

        except Exception as e:
            logger.error(f"断开连接失败: {e}")

    def register_task_handler(self, task_type: str, handler: Callable) -> None:
        """注册任务处理器

        Args:
            task_type: 任务类型
            handler: 处理函数，签名为 async def handler(task_status: TaskStatus) -> Any
        """
        self.task_handlers[task_type] = handler
        logger.info(f"注册任务处理器: {task_type}")

    async def create_consumer_group(
        self,
        group_name: str,
        consumer_name: Optional[str] = None,
        start_id: str = "0",
    ) -> ConsumerGroup:
        """创建消费者组

        Args:
            group_name: 消费者组名称
            consumer_name: 消费者名称
            start_id: 开始读取的消息ID

        Returns:
            消费者组实例
        """
        if not self.redis_client:
            raise RuntimeError("Redis未连接")

        # 为每个优先级队列创建消费者组
        for priority in TaskPriority:
            queue_name = self.task_queue._get_queue_name(priority)

            consumer_group = ConsumerGroup(
                self.redis_client, queue_name, group_name, consumer_name
            )

            await consumer_group.create_group(start_id)

            # 只保存一个代表性的消费者组用于后续操作
            if priority == TaskPriority.NORMAL:
                self.consumer_groups[group_name] = consumer_group

        logger.info(f"创建消费者组: {group_name}")
        return self.consumer_groups[group_name]

    async def start_consumer(
        self,
        group_name: str,
        consumer_name: Optional[str] = None,
        max_concurrent_tasks: int = 10,
    ) -> None:
        """启动消费者

        Args:
            group_name: 消费者组名称
            consumer_name: 消费者名称
            max_concurrent_tasks: 最大并发任务数
        """
        if not self.task_queue:
            raise RuntimeError("任务队列未初始化")

        # 创建消费者组（如果不存在）
        consumer_group = await self.create_consumer_group(group_name, consumer_name)

        # 启动消费者任务
        consumer_task = asyncio.create_task(
            self._consumer_loop(consumer_group, max_concurrent_tasks)
        )

        self._background_tasks.append(consumer_task)
        logger.info(f"启动消费者: {group_name}/{consumer_group.consumer_name}")

    async def _consumer_loop(
        self,
        consumer_group: ConsumerGroup,
        max_concurrent_tasks: int,
    ) -> None:
        """消费者循环"""
        semaphore = asyncio.Semaphore(max_concurrent_tasks)

        while self._running:
            try:
                # 从队列中取出任务
                task_status = await self.task_queue.dequeue(consumer_group)

                if task_status:
                    # 创建任务处理协程
                    task_coroutine = self._process_task(task_status, semaphore)
                    asyncio.create_task(task_coroutine)
                else:
                    # 没有任务时短暂休眠
                    await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"消费者循环异常: {e}")
                await asyncio.sleep(1)

    async def _process_task(
        self, task_status: TaskStatus, semaphore: asyncio.Semaphore
    ) -> None:
        """处理单个任务"""
        async with semaphore:
            try:
                # 查找任务处理器
                handler = self.task_handlers.get(task_status.task_type)
                if not handler:
                    await self.task_queue.nack(
                        task_status,
                        f"未找到任务类型 {task_status.task_type} 的处理器",
                        retry=False,
                    )
                    return

                # 执行任务
                result = await handler(task_status)

                # 确认任务完成
                await self.task_queue.ack(task_status, {"result": result})

            except Exception as e:
                logger.error(f"任务处理异常: {e}")

                # 拒绝任务并可能重试
                await self.task_queue.nack(
                    task_status,
                    str(e),
                    error_traceback=str(e.__traceback__) if e.__traceback__ else None,
                )

    async def start_background_tasks(self) -> None:
        """启动后台任务"""
        self._running = True

        # 启动计划任务处理器
        scheduler_task = asyncio.create_task(self._scheduler_loop())
        self._background_tasks.append(scheduler_task)

        # 启动清理任务
        cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._background_tasks.append(cleanup_task)

        logger.info("后台任务已启动")

    async def stop_background_tasks(self) -> None:
        """停止后台任务"""
        self._running = False

        # 取消所有后台任务
        for task in self._background_tasks:
            task.cancel()

        # 等待任务完成
        if self._background_tasks:
            await asyncio.gather(*self._background_tasks, return_exceptions=True)

        self._background_tasks.clear()
        logger.info("后台任务已停止")

    async def _scheduler_loop(self) -> None:
        """计划任务处理循环"""
        while self._running:
            try:
                if self.task_queue:
                    await self.task_queue.process_scheduled_tasks()
                await asyncio.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logger.error(f"计划任务处理异常: {e}")
                await asyncio.sleep(10)

    async def _cleanup_loop(self) -> None:
        """清理任务循环"""
        while self._running:
            try:
                if self.task_queue:
                    await self.task_queue.cleanup_completed_tasks()
                await asyncio.sleep(3600)  # 每小时清理一次

            except Exception as e:
                logger.error(f"清理任务异常: {e}")
                await asyncio.sleep(3600)

    # 便捷方法

    async def enqueue_task(
        self, task_type: str, payload: Dict[str, Any], **kwargs
    ) -> str:
        """入队任务的便捷方法"""
        if not self.task_queue:
            raise RuntimeError("任务队列未初始化")

        return await self.task_queue.enqueue(task_type, payload, **kwargs)

    async def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态的便捷方法"""
        if not self.task_queue:
            raise RuntimeError("任务队列未初始化")

        return await self.task_queue.get_task_status(task_id)

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务的便捷方法"""
        if not self.task_queue:
            raise RuntimeError("任务队列未初始化")

        return await self.task_queue.cancel_task(task_id)

    async def get_queue_info(self) -> Dict[str, Any]:
        """获取队列信息的便捷方法"""
        if not self.task_queue:
            raise RuntimeError("任务队列未初始化")

        return await self.task_queue.get_queue_info()
