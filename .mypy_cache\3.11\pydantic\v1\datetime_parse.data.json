{".class": "MypyFile", "_fullname": "pydantic.v1.datetime_parse", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EPOCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.EPOCH", "name": "EPOCH", "setter_type": null, "type": "datetime.datetime"}}, "MAX_NUMBER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.MAX_NUMBER", "name": "MAX_NUMBER", "setter_type": null, "type": "builtins.int"}}, "MS_WATERSHED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.MS_WATERSHED", "name": "MS_WATERSHED", "setter_type": null, "type": "builtins.int"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrBytesIntFloat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.v1.datetime_parse.StrBytesIntFloat", "line": 63, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.int", "builtins.float"], "uses_pep604_syntax": false}}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.datetime_parse.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.datetime_parse.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.datetime_parse.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.datetime_parse.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.datetime_parse.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.datetime_parse.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_parse_timezone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["value", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.datetime_parse._parse_timezone", "name": "_parse_timezone", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["value", "error"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "builtins.Exception"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_timezone", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", "datetime.timezone"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef", "module_hidden": true, "module_public": false}, "date_expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.date_expr", "name": "date_expr", "setter_type": null, "type": "builtins.str"}}, "date_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.date_re", "name": "date_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.datetime_re", "name": "datetime_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "errors": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors", "kind": "Gdef", "module_hidden": true, "module_public": false}, "from_unix_seconds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["seconds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.datetime_parse.from_unix_seconds", "name": "from_unix_seconds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["seconds"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_unix_seconds", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_numeric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["value", "native_expected_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.datetime_parse.get_numeric", "name": "get_numeric", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["value", "native_expected_type"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.datetime_parse.StrBytesIntFloat"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_numeric", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", "builtins.float"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iso8601_duration_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.iso8601_duration_re", "name": "iso8601_duration_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "parse_date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.datetime_parse.parse_date", "name": "parse_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["datetime.date", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.datetime_parse.StrBytesIntFloat"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_date", "ret_type": "datetime.date", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_datetime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.datetime_parse.parse_datetime", "name": "parse_datetime", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["datetime.datetime", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.datetime_parse.StrBytesIntFloat"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_datetime", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_duration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.datetime_parse.parse_duration", "name": "parse_duration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.datetime_parse.StrBytesIntFloat"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_duration", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.datetime_parse.parse_time", "name": "parse_time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["datetime.time", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.datetime_parse.StrBytesIntFloat"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_time", "ret_type": "datetime.time", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_hidden": true, "module_public": false}, "standard_duration_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.standard_duration_re", "name": "standard_duration_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "time_expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.time_expr", "name": "time_expr", "setter_type": null, "type": "builtins.str"}}, "time_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.datetime_parse.time_re", "name": "time_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py"}