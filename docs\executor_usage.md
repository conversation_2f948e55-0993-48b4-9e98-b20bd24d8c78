# TaskExecutor 使用指南

TaskExecutor是数据采集任务执行器，实现了完整的数据采集到存储流程，整合了爬虫、清洗、存储功能。

## 功能特性

- **完整数据流程**: 爬取 → 清洗 → 存储的端到端处理
- **配置驱动**: 支持JSON/YAML配置文件定义任务
- **异步执行**: 基于asyncio的高性能异步处理
- **批量处理**: 支持并发执行多个任务
- **错误处理**: 完善的重试机制和错误恢复
- **执行钩子**: 支持在各个阶段插入自定义逻辑
- **监控指标**: 详细的执行统计和日志记录
- **资源管理**: 自动管理连接池和资源释放

## 快速开始

### 1. 基本使用

```python
import asyncio
from src.data_trans.executor import TaskExecutor, TaskExecutorConfig

async def main():
    # 创建执行器配置
    config = TaskExecutorConfig(
        max_concurrent_tasks=5,
        timeout=300.0,
        enable_metrics=True
    )
    
    # 创建执行器
    executor = TaskExecutor(config)
    
    # 定义任务配置
    task_config = {
        "task_id": "demo_task",
        "crawler_type": "web",
        "urls": ["https://httpbin.org/json"],
        "cleaner_type": "text",
        "storage_type": "mongodb",
        "raw_collection": "demo_raw",
        "cleaned_collection": "demo_cleaned"
    }
    
    # 执行任务
    result = await executor.execute_task("demo_task", task_config)
    
    print(f"任务状态: {result.status.value}")
    print(f"爬取数据: {result.crawled_count} 条")
    print(f"清洗数据: {result.cleaned_count} 条")
    print(f"存储数据: {result.stored_count} 条")
    
    await executor.cleanup()

asyncio.run(main())
```

### 2. 配置文件方式

创建任务配置文件 `task_config.json`:

```json
{
  "task_id": "example_task",
  "crawler_type": "web",
  "urls": [
    "https://httpbin.org/json",
    "https://httpbin.org/headers"
  ],
  "cleaner_type": "text",
  "cleaning_rules": [
    {
      "name": "normalize_whitespace",
      "field": "data",
      "rule_type": "regex",
      "pattern": "\\s+",
      "replacement": " ",
      "priority": 1
    }
  ],
  "storage_type": "mongodb",
  "raw_collection": "example_raw",
  "cleaned_collection": "example_cleaned"
}
```

使用配置文件执行:

```python
async def main():
    executor = TaskExecutor(TaskExecutorConfig())
    result = await executor.execute_from_file("task_config.json")
    await executor.cleanup()
```

### 3. 批量执行

```python
async def main():
    executor = TaskExecutor(TaskExecutorConfig(max_concurrent_tasks=3))
    
    tasks = [
        {"task_id": "task1", "urls": ["https://example1.com"]},
        {"task_id": "task2", "urls": ["https://example2.com"]},
        {"task_id": "task3", "urls": ["https://example3.com"]},
    ]
    
    results = await executor.execute_batch(tasks)
    
    for result in results:
        print(f"{result.task_id}: {result.status.value}")
    
    await executor.cleanup()
```

## 命令行工具

TaskExecutor提供了命令行工具，方便在脚本和CI/CD中使用。

### 安装

```bash
pip install -e .
```

### 使用方法

#### 1. 执行单个任务

```bash
# 从配置文件执行任务
python -m src.data_trans.executor.cli run -c task_config.json

# 指定超时时间和并发数
python -m src.data_trans.executor.cli run -c task_config.json -t 600 -m 5
```

#### 2. 批量执行

```bash
# 执行目录中的所有JSON配置文件
python -m src.data_trans.executor.cli batch -d ./configs/

# 指定文件模式
python -m src.data_trans.executor.cli batch -d ./configs/ -p "*.json" -m 3
```

#### 3. 生成配置模板

```bash
# 生成Web爬虫配置模板
python -m src.data_trans.executor.cli template -o my_task.json --crawler-type web

# 生成API爬虫配置模板
python -m src.data_trans.executor.cli template -o api_task.json --crawler-type api
```

## 配置说明

### TaskExecutorConfig

执行器全局配置:

```python
config = TaskExecutorConfig(
    max_retries=3,              # 最大重试次数
    retry_delay=1.0,            # 重试延迟(秒)
    timeout=300.0,              # 执行超时时间(秒)
    max_concurrent_tasks=10,    # 最大并发任务数
    log_level="INFO",           # 日志级别
    enable_metrics=True,        # 启用监控指标
    enable_hooks=True,          # 启用执行钩子
    default_storage_type="mongodb",     # 默认存储类型
    raw_data_collection="raw_data",     # 原始数据集合名
    cleaned_data_collection="cleaned_data"  # 清洗数据集合名
)
```

### TaskConfig

单个任务配置:

```python
task_config = {
    "task_id": "unique_task_id",        # 任务ID
    "task_type": "crawl_clean_store",   # 任务类型
    
    # 爬虫配置
    "crawler_type": "web",              # 爬虫类型: web, api
    "crawler_config": {                 # 爬虫参数
        "timeout": 30.0,
        "max_retries": 3,
        "rate_limit_per_second": 1.0
    },
    "urls": ["https://example.com"],    # URL列表
    
    # 清洗配置
    "cleaner_type": "text",             # 清洗器类型
    "cleaner_config": {                 # 清洗器参数
        "strict_mode": False,
        "skip_invalid": True
    },
    "cleaning_rules": [                 # 清洗规则
        {
            "name": "rule_name",
            "field": "field_name",
            "rule_type": "regex",
            "pattern": "pattern",
            "replacement": "replacement"
        }
    ],
    
    # 存储配置
    "storage_type": "mongodb",          # 存储类型
    "storage_config": {},               # 存储参数
    "raw_collection": "raw_data",       # 原始数据集合
    "cleaned_collection": "cleaned_data" # 清洗数据集合
}
```

## 执行钩子

TaskExecutor支持在执行过程中的各个阶段插入自定义逻辑:

- `before_crawl`: 爬取前钩子
- `after_crawl`: 爬取后钩子
- `before_clean`: 清洗前钩子
- `after_clean`: 清洗后钩子
- `before_store`: 存储前钩子
- `after_store`: 存储后钩子

可以通过继承TaskExecutor并重写`_execute_hook`方法来实现自定义钩子逻辑。

## 监控和日志

### 执行结果

每个任务执行后会返回详细的执行结果:

```python
result = await executor.execute_task(task_id, config)

print(f"任务ID: {result.task_id}")
print(f"状态: {result.status.value}")
print(f"开始时间: {result.start_time}")
print(f"结束时间: {result.end_time}")
print(f"执行时长: {result.duration} 秒")
print(f"爬取数据: {result.crawled_count} 条")
print(f"清洗数据: {result.cleaned_count} 条")
print(f"存储数据: {result.stored_count} 条")

if result.error_message:
    print(f"错误信息: {result.error_message}")

# 查看执行日志
for log in result.logs:
    print(log)
```

### 执行统计

获取整体执行统计信息:

```python
stats = executor.get_execution_stats()
print(f"总任务数: {stats['total_tasks']}")
print(f"成功率: {stats['success_rate']:.2%}")
print(f"平均执行时间: {stats['average_duration']:.2f} 秒")
print(f"总爬取数据: {stats['total_crawled']} 条")
```

## 最佳实践

1. **合理设置并发数**: 根据目标网站的承载能力设置`max_concurrent_tasks`
2. **配置重试机制**: 为不稳定的数据源设置适当的重试次数和延迟
3. **监控资源使用**: 定期检查内存和连接池使用情况
4. **错误处理**: 在钩子函数中实现自定义错误处理逻辑
5. **日志记录**: 启用结构化日志，便于问题排查
6. **配置管理**: 使用配置文件管理复杂的任务参数
7. **资源清理**: 确保在程序结束时调用`cleanup()`方法

## 故障排查

常见问题和解决方案:

1. **连接超时**: 检查网络连接和目标服务器状态
2. **内存不足**: 减少并发任务数或增加系统内存
3. **存储失败**: 检查数据库连接和权限配置
4. **清洗错误**: 验证清洗规则的正确性
5. **任务卡死**: 检查超时设置和目标URL的响应时间
