{".class": "MypyFile", "_fullname": "pydantic.functional_serializers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers.AnyType", "name": "AnyType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FieldPlainSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.FieldPlainSerializer", "line": 194, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}}}, "FieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.FieldSerializer", "line": 200, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.FieldPlainSerializer"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.FieldWrapSerializer"}], "uses_pep604_syntax": true}}}, "FieldWrapSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.FieldWrapSerializer", "line": 197, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}}}, "GetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetCoreSchemaHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModelPlainSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.ModelPlainSerializer", "line": 309, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}}}, "ModelPlainSerializerWithInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.ModelPlainSerializerWithInfo", "line": 303, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializationInfo"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ModelPlainSerializerWithoutInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo", "line": 306, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ModelSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.ModelSerializer", "line": 321, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializer"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializer"}], "uses_pep604_syntax": true}}}, "ModelWrapSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.ModelWrapSerializer", "line": 318, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}}}, "ModelWrapSerializerWithInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.ModelWrapSerializerWithInfo", "line": 312, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializerFunctionWrapHandler", "pydantic_core.core_schema.SerializationInfo"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ModelWrapSerializerWithoutInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo", "line": 315, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializerFunctionWrapHandler"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PlainSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_serializers.PlainSerializer", "name": "PlainSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.functional_serializers.PlainSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "func", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "return_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "when_used", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "pydantic.functional_serializers", "mro": ["pydantic.functional_serializers.PlainSerializer", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.functional_serializers.PlainSerializer.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.functional_serializers.PlainSerializer.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "arg_types": ["pydantic.functional_serializers.PlainSerializer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of PlainSerializer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "return_type", "when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.functional_serializers.PlainSerializer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "return_type", "when_used"], "arg_types": ["pydantic.functional_serializers.PlainSerializer", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PlainSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.functional_serializers.PlainSerializer.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "func"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "return_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "when_used"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["func", "return_type", "when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.functional_serializers.PlainSerializer.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["func", "return_type", "when_used"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of PlainSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.functional_serializers.PlainSerializer.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["func", "return_type", "when_used"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of PlainSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "pydantic.functional_serializers.PlainSerializer.func", "name": "func", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}}}, "return_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "pydantic.functional_serializers.PlainSerializer.return_type", "name": "return_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "when_used": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "pydantic.functional_serializers.PlainSerializer.when_used", "name": "when_used", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers.PlainSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_serializers.PlainSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PydanticUndefinedAnnotation": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUndefinedAnnotation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SerializationInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.SerializationInfo", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SerializeAsAny": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.functional_serializers.SerializeAsAny", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 4, "fullname": "pydantic.functional_serializers.SerializeAsAny", "line": 424, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.functional_serializers.SerializeAsAny", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "SerializerFunctionWrapHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.SerializerFunctionWrapHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WhenUsed": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.WhenUsed", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WrapSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_serializers.WrapSerializer", "name": "WrapSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.functional_serializers.WrapSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 152, "name": "func", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 153, "name": "return_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 154, "name": "when_used", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "pydantic.functional_serializers", "mro": ["pydantic.functional_serializers.WrapSerializer", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.functional_serializers.WrapSerializer.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.functional_serializers.WrapSerializer.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "arg_types": ["pydantic.functional_serializers.WrapSerializer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of WrapSerializer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "return_type", "when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.functional_serializers.WrapSerializer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "return_type", "when_used"], "arg_types": ["pydantic.functional_serializers.WrapSerializer", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of WrapSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.functional_serializers.WrapSerializer.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "func"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "return_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "when_used"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["func", "return_type", "when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.functional_serializers.WrapSerializer.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["func", "return_type", "when_used"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of WrapSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.functional_serializers.WrapSerializer.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["func", "return_type", "when_used"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of WrapSerializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "pydantic.functional_serializers.WrapSerializer.func", "name": "func", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}}}, "return_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "pydantic.functional_serializers.WrapSerializer.return_type", "name": "return_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "when_used": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "pydantic.functional_serializers.WrapSerializer.when_used", "name": "when_used", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers.WrapSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_serializers.WrapSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FieldPlainSerializerT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "name": "_FieldPlainSerializerT", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_FieldWrapSerializerT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "name": "_FieldWrapSerializerT", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_ModelPlainSerializerT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "name": "_ModelPlainSerializerT", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_ModelWrapSerializerT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "name": "_ModelWrapSerializerT", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_Partial": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_serializers._Partial", "line": 192, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "functools.partial"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "functools.partialmethod"}], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_serializers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_serializers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_serializers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_serializers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_serializers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_serializers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_internal_dataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._internal_dataclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_hidden": true, "module_public": false}, "field_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.functional_serializers.field_serializer", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 5, 5, 5], "arg_names": ["fields", "mode", "return_type", "when_used", "check_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.functional_serializers.field_serializer", "name": "field_serializer", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5, 5, 5], "arg_names": ["fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_serializer", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_serializers.field_serializer", "name": "field_serializer", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_serializers.field_serializer", "name": "field_serializer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_serializers.field_serializer", "name": "field_serializer", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_serializers.field_serializer", "name": "field_serializer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldWrapSerializerT", "id": -1, "name": "_FieldWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5], "arg_names": [null, "fields", "mode", "return_type", "when_used", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._FieldPlainSerializerT", "id": -1, "name": "_FieldPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers._Partial"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "model_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.functional_serializers.model_serializer", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5], "arg_names": [null, "mode", "when_used", "return_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.functional_serializers.model_serializer", "name": "model_serializer", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5], "arg_names": [null, "mode", "when_used", "return_type"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -2, "name": "_ModelWrapSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -2, "name": "_ModelWrapSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -2, "name": "_ModelWrapSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -2, "name": "_ModelWrapSerializerT", "namespace": "pydantic.functional_serializers.model_serializer", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_serializers.model_serializer", "name": "model_serializer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_serializers.model_serializer", "name": "model_serializer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_serializers.model_serializer", "name": "model_serializer", "type": {".class": "CallableType", "arg_kinds": [3, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_serializers.model_serializer", "name": "model_serializer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_serializers.model_serializer", "name": "model_serializer", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_serializers.model_serializer", "name": "model_serializer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "pydantic.functional_serializers.model_serializer#0", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [3, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelWrapSerializerT", "id": -1, "name": "_ModelWrapSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelWrapSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["mode", "when_used", "return_type"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_serializer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_serializers._ModelPlainSerializerT", "id": -1, "name": "_ModelPlainSerializerT", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithInfo"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_serializers.ModelPlainSerializerWithoutInfo"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_hidden": true, "module_public": false}, "partialmethod": {".class": "SymbolTableNode", "cross_ref": "functools.partialmethod", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\pydantic\\functional_serializers.py"}